# API Documentation - Minitab-like Variability Plot Web UI

## Overview

This document provides comprehensive API documentation for developers who want to understand, modify, or extend the Minitab-like Variability Plot Web UI application.

## Architecture

### Module Structure

```
src/
├── data/           # Data processing and statistical calculations
├── charts/         # Chart creation and management
├── components/     # UI components and managers
├── utils/          # Utility functions and helpers
└── styles/         # CSS styling
```

### Core Dependencies

- **Chart.js**: Chart rendering and visualization
- **Vite**: Build tool and development server
- **Vitest**: Testing framework
- **ESLint**: Code linting
- **Prettier**: Code formatting

## Data Processing API

### DataProcessor Class

**Location**: `src/data/dataProcessor.js`

#### Constructor
```javascript
const processor = new DataProcessor();
```

#### Methods

##### `parseStringData(input: string): number[]`
Parses string input into numerical array.

**Parameters**:
- `input`: String containing numbers separated by spaces, commas, or semicolons

**Returns**: Array of numbers

**Throws**: Error if input is invalid or contains non-numerical values

**Example**:
```javascript
const data = processor.parseStringData("1 2 3 4 5");
// Returns: [1, 2, 3, 4, 5]
```

##### `parseCSVData(csvText: string): Object`
Parses CSV content into structured data.

**Parameters**:
- `csvText`: CSV content as string

**Returns**: Object with `headers` and `rows` arrays

**Example**:
```javascript
const csv = "Value,Group\n1,A\n2,B";
const result = processor.parseCSVData(csv);
// Returns: { headers: ["Value", "Group"], rows: [["1", "A"], ["2", "B"]] }
```

##### `setData(rawData: any[], groups: string[] = []): void`
Sets and validates the dataset.

**Parameters**:
- `rawData`: Array of raw data values
- `groups`: Optional array of group labels

**Side Effects**: Updates internal data and metadata

##### `getStatistics(): Object`
Calculates descriptive statistics for the dataset.

**Returns**: Object containing statistical measures

**Example**:
```javascript
const stats = processor.getStatistics();
// Returns: { count, mean, median, standardDeviation, ... }
```

##### `getPlotData(): Object`
Generates data formatted for plotting.

**Returns**: Object with raw data, statistics, and plot-specific data

##### `exportData(format: string): string`
Exports data in specified format.

**Parameters**:
- `format`: Export format ('csv', 'json', 'txt')

**Returns**: Formatted data string

## Statistical Functions API

**Location**: `src/data/statistics.js`

### Core Functions

#### `calculateDescriptiveStats(data: number[]): Object`
Calculates comprehensive descriptive statistics.

**Parameters**:
- `data`: Array of numerical values

**Returns**: Object with statistical measures

**Properties**:
```javascript
{
  count: number,
  mean: number,
  median: number,
  mode: number[],
  standardDeviation: number,
  variance: number,
  min: number,
  max: number,
  range: number,
  q1: number,
  q3: number,
  iqr: number,
  skewness: number,
  kurtosis: number
}
```

#### `calculateMean(data: number[]): number`
Calculates arithmetic mean.

#### `calculateMedian(sortedData: number[]): number`
Calculates median value.

#### `calculateStandardDeviation(data: number[], sample: boolean = true): number`
Calculates standard deviation.

**Parameters**:
- `data`: Array of numbers
- `sample`: Whether to use sample standard deviation (default: true)

#### `calculateConfidenceInterval(data: number[], confidence: number = 0.95): Object`
Calculates confidence interval for the mean.

**Returns**:
```javascript
{
  lower: number,
  upper: number,
  margin: number
}
```

## Chart Management API

### ChartManager Class

**Location**: `src/charts/chartManager.js`

#### Constructor
```javascript
const chartManager = new ChartManager();
```

#### Methods

##### `createHistogram(canvasId: string, histogramData: Object, options: Object = {}): Chart`
Creates a histogram chart.

**Parameters**:
- `canvasId`: ID of canvas element
- `histogramData`: Object with `bins`, `labels`, `binWidth`, `min`, `max`
- `options`: Chart customization options

**Returns**: Chart.js instance

##### `createBoxPlot(canvasId: string, boxplotData: Object, options: Object = {}): Chart`
Creates a box plot chart.

**Parameters**:
- `canvasId`: ID of canvas element
- `boxplotData`: Object with quartiles, outliers, and whisker data
- `options`: Chart customization options

##### `createVariabilityPlot(canvasId: string, data: Object, options: Object = {}): Chart`
Creates a variability plot.

**Parameters**:
- `canvasId`: ID of canvas element
- `data`: Object with raw data, statistics, and confidence intervals
- `options`: Chart customization options

##### `destroyChart(canvasId: string): void`
Destroys a specific chart.

##### `exportChart(canvasId: string, format: string = 'png'): string`
Exports chart as image.

**Returns**: Data URL of the image

### VariabilityPlot Class

**Location**: `src/charts/variabilityPlot.js`

#### Constructor
```javascript
const variabilityPlot = new VariabilityPlot(chartManager);
```

#### Methods

##### `createIndividualPlot(canvasId: string, plotData: Object, options: Object = {}): Chart`
Creates an Individual Values (I-Chart) control chart.

**Features**:
- Control limits (±3σ)
- Center line (mean)
- Out-of-control point detection

##### `createMovingRangePlot(canvasId: string, plotData: Object, options: Object = {}): Chart`
Creates a Moving Range (MR-Chart) control chart.

**Features**:
- Moving range calculation
- Control limits for ranges
- Process variation monitoring

##### `analyzeControlPatterns(data: number[], ucl: number, lcl: number, centerLine: number): Object`
Analyzes control chart patterns for special causes.

**Returns**: Object with detected patterns and violations

## Utility APIs

### NotificationManager Class

**Location**: `src/utils/notifications.js`

#### Methods

##### `show(message: string, type: string = 'info', duration: number = 5000, options: Object = {}): string`
Shows a notification.

**Parameters**:
- `message`: Notification text
- `type`: Notification type ('success', 'error', 'warning', 'info')
- `duration`: Auto-dismiss time in milliseconds (0 for persistent)
- `options`: Additional options

**Returns**: Notification ID

##### `success(message: string, duration: number = 5000): string`
Shows success notification.

##### `error(message: string, duration: number = 0): string`
Shows error notification (persistent by default).

##### `remove(id: string): void`
Removes specific notification.

##### `clear(): void`
Removes all notifications.

### FileManager Class

**Location**: `src/utils/fileManager.js`

#### Methods

##### `readFileAsText(file: File): Promise<string>`
Reads file content as text.

##### `downloadFile(content: string, filename: string, mimeType: string = 'text/plain'): void`
Downloads content as file.

##### `parseCSV(csvContent: string): Object`
Parses CSV content into structured data.

##### `createJSON(data: any, pretty: boolean = true): string`
Creates JSON string from data.

##### `validateFileType(file: File, allowedTypes: string[]): boolean`
Validates file type.

##### `formatFileSize(bytes: number): string`
Formats file size for display.

### SampleDataGenerator Class

**Location**: `src/utils/sampleData.js`

#### Methods

##### `generateNormalDistribution(count: number = 50, mean: number = 100, stdDev: number = 15): number[]`
Generates normally distributed data using Box-Muller transform.

##### `generateSkewedData(count: number = 40, lambda: number = 0.5): number[]`
Generates right-skewed data using exponential distribution.

##### `generateGroupedData(options: Object = {}): Object`
Generates grouped data with different means.

**Returns**: Object with `data` and `groups` arrays

##### `generateDataWithOutliers(count: number = 30, outlierPercent: number = 10): number[]`
Generates data with specified percentage of outliers.

##### `generateControlChartData(count: number = 100, options: Object = {}): Object`
Generates control chart data with optional special causes.

## Event System

### Custom Events

The application uses custom events for component communication:

#### `chartResize`
Triggered when window is resized.

```javascript
document.addEventListener('chartResize', () => {
  // Handle chart resize
});
```

#### `dataLoaded`
Triggered when new data is loaded.

```javascript
document.addEventListener('dataLoaded', (event) => {
  const { data, statistics } = event.detail;
  // Handle new data
});
```

## Configuration Options

### Chart Options

Common options for all chart types:

```javascript
const options = {
  title: 'Chart Title',
  xAxisLabel: 'X Axis Label',
  yAxisLabel: 'Y Axis Label',
  showLegend: true,
  showGrid: true,
  backgroundColor: '#3498db',
  borderColor: '#2980b9',
  confidence: 0.95,
  showMean: true,
  showConfidenceInterval: false
};
```

### Notification Options

```javascript
const options = {
  closable: true,
  showProgress: false,
  actions: [
    {
      label: 'Action',
      type: 'primary',
      handler: () => { /* action */ },
      dismiss: true
    }
  ]
};
```

## Error Handling

### Error Types

#### `DataValidationError`
Thrown when data validation fails.

#### `ChartCreationError`
Thrown when chart creation fails.

#### `FileProcessingError`
Thrown when file operations fail.

### Error Handling Patterns

```javascript
try {
  const data = processor.parseStringData(input);
  processor.setData(data);
} catch (error) {
  if (error instanceof DataValidationError) {
    // Handle validation error
  } else {
    // Handle other errors
  }
}
```

## Performance Considerations

### Large Datasets

For datasets > 10,000 points:
- Use data sampling
- Implement virtual scrolling
- Consider data aggregation

### Memory Management

- Destroy charts when no longer needed
- Clear notification history
- Remove event listeners on cleanup

### Optimization Tips

1. **Debounce user input** for real-time validation
2. **Lazy load** chart libraries
3. **Cache calculations** for repeated operations
4. **Use Web Workers** for heavy computations

## Extension Points

### Adding New Chart Types

1. Extend `ChartManager` class
2. Implement chart creation method
3. Add to chart type selector
4. Update tests

### Adding New Statistical Functions

1. Add function to `statistics.js`
2. Update `calculateDescriptiveStats`
3. Add comprehensive tests
4. Update documentation

### Custom Data Sources

1. Implement data adapter interface
2. Add to `DataProcessor`
3. Handle validation and conversion
4. Add error handling

## Testing API

### Test Utilities

**Location**: `tests/`

#### Mock Objects

```javascript
// Chart.js mock
const mockChart = vi.fn().mockImplementation((ctx, config) => ({
  destroy: vi.fn(),
  update: vi.fn(),
  toBase64Image: vi.fn(() => 'data:image/png;base64,mock')
}));
```

#### Test Data Generators

```javascript
// Generate test data
const testData = sampleData.generateNormalDistribution(100, 50, 10);
```

### Running Tests

```bash
# All tests
npm test

# Specific test file
npm test statistics.test.js

# Coverage report
npm run test:coverage
```

## Deployment

### Build Process

```bash
# Development build
npm run dev

# Production build
npm run build

# Preview production build
npm run preview
```

### Environment Variables

```javascript
// vite.config.js
export default defineConfig({
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
  }
});
```

### Browser Support

**Minimum Requirements**:
- ES2020 support
- Canvas API
- File API
- Fetch API

**Polyfills**: Not required for modern browsers

## Security Considerations

### File Upload Security

- Validate file types
- Limit file sizes
- Sanitize file content
- No server-side processing

### Data Privacy

- All processing is client-side
- No data transmission to servers
- Local storage only for preferences

### XSS Prevention

- Sanitize user input
- Use textContent instead of innerHTML
- Validate all data inputs
