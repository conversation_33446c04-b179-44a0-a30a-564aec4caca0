<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Chart Test</title>
</head>
<body>
  <h1>Simple Chart Test</h1>
  <canvas id="test-chart" width="400" height="200"></canvas>
  <button id="create-chart">Create Chart</button>

  <script type="module">
    import {
      Chart,
      CategoryScale,
      LinearScale,
      BarElement,
      Title,
      Tooltip,
      Legend
    } from 'chart.js';

    // Register Chart.js components
    Chart.register(
      CategoryScale,
      LinearScale,
      BarElement,
      Title,
      Tooltip,
      Legend
    );

    console.log('Chart.js loaded:', Chart);

    document.getElementById('create-chart').addEventListener('click', () => {
      console.log('Creating test chart...');
      
      const canvas = document.getElementById('test-chart');
      const ctx = canvas.getContext('2d');
      
      try {
        const chart = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: ['A', 'B', 'C', 'D'],
            datasets: [{
              label: 'Test Data',
              data: [12, 19, 3, 5],
              backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 205, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)'
              ],
              borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 205, 86, 1)',
                'rgba(75, 192, 192, 1)'
              ],
              borderWidth: 1
            }]
          },
          options: {
            responsive: false,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
        
        console.log('Chart created successfully:', chart);
      } catch (error) {
        console.error('Error creating chart:', error);
      }
    });
  </script>
</body>
</html>
