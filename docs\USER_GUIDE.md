# User Guide - Minitab-like Variability Plot Web UI

## Overview

The Minitab-like Variability Plot Web UI is a comprehensive statistical analysis tool that provides powerful data visualization and analysis capabilities similar to Minitab software, but accessible through any modern web browser.

## Getting Started

### Accessing the Application

1. Open your web browser
2. Navigate to the application URL
3. The application loads with three main tabs: **Data Input**, **Analysis**, and **Export**

### Quick Start

1. **Load Data**: Go to the Data Input tab and enter your data
2. **Analyze**: Switch to the Analysis tab and select a plot type
3. **Generate**: Click "Generate Plot" to create your visualization
4. **Export**: Use the Export tab to save your results

## Data Input Methods

### 1. Manual Data Entry

**Location**: Data Input tab → Manual Entry section

**Steps**:
1. Enter numerical data in the text area
2. Separate values with spaces, commas, or semicolons
3. Optionally add group labels in the second text area
4. Click "Load Data"

**Examples**:
```
Data: 1 2 3 4 5 6 7 8 9 10
Groups: A A A B B B C C C C
```

**Supported Formats**:
- Space separated: `1 2 3 4 5`
- Comma separated: `1,2,3,4,5`
- Mixed separators: `1, 2; 3 4`

### 2. CSV File Upload

**Location**: Data Input tab → CSV File Upload section

**Steps**:
1. Click "Choose CSV File"
2. Select a CSV file from your computer
3. Preview the data in the table
4. Data is automatically loaded

**CSV Format Requirements**:
- First column: numerical data values
- Second column (optional): group labels
- Headers are automatically detected

**Example CSV**:
```csv
Value,Group
23.5,A
24.1,A
22.8,B
25.2,B
```

### 3. Sample Data

**Location**: Data Input tab → Sample Data section

**Available Datasets**:
- **Normal Distribution**: Standard bell curve data
- **Skewed Data**: Right-skewed distribution
- **Grouped Data**: Multiple groups with different means
- **Data with Outliers**: Normal data with extreme values

**Usage**: Click any sample data button to instantly load example data

## Analysis Features

### Plot Types

#### 1. Histogram
**Purpose**: Shows the distribution of your data
**Best for**: Understanding data shape, identifying patterns
**Features**:
- Automatic bin calculation
- Customizable bin count
- Frequency display

#### 2. Box Plot
**Purpose**: Displays five-number summary and outliers
**Best for**: Identifying outliers, comparing distributions
**Features**:
- Quartiles (Q1, Q3)
- Median line
- Whiskers showing data range
- Outlier identification

#### 3. Variability Plot
**Purpose**: Shows data points over time or sequence
**Best for**: Trend analysis, pattern recognition
**Features**:
- Individual data points
- Mean line
- Confidence intervals
- Trend visualization

#### 4. Individual Values Chart (I-Chart)
**Purpose**: Control chart for individual measurements
**Best for**: Process monitoring, quality control
**Features**:
- Control limits (±3σ)
- Center line (mean)
- Out-of-control point identification
- Statistical process control

#### 5. Moving Range Chart (MR-Chart)
**Purpose**: Shows variation between consecutive points
**Best for**: Process stability assessment
**Features**:
- Moving range calculation
- Control limits
- Process variation monitoring

#### 6. Scatter Plot
**Purpose**: Shows relationship between groups
**Best for**: Comparing multiple groups
**Features**:
- Color-coded groups
- Individual point display
- Group comparison

### Plot Customization

#### Chart Options
- **Chart Title**: Custom title for your plot
- **Confidence Level**: 80%, 90%, 95%, or 99%
- **Show Mean Line**: Display average value
- **Show Confidence Interval**: Display confidence bands
- **Show Grid**: Display grid lines for easier reading
- **Show Legend**: Display chart legend

#### Interactive Features
- **Zoom**: Mouse wheel to zoom in/out
- **Pan**: Click and drag to move around
- **Hover**: Hover over points for detailed information
- **Fullscreen**: Click fullscreen button for larger view

## Statistical Information

### Descriptive Statistics

The application automatically calculates:

**Central Tendency**:
- Mean (average)
- Median (middle value)
- Mode (most frequent value)

**Variability**:
- Standard deviation
- Variance
- Range (max - min)
- Interquartile range (IQR)

**Distribution Shape**:
- Skewness (asymmetry)
- Kurtosis (tail heaviness)

**Position**:
- Minimum value
- Maximum value
- First quartile (Q1)
- Third quartile (Q3)

### Quality Control Statistics

For control charts:
- **Control Limits**: ±3 standard deviations from mean
- **Center Line**: Process average
- **Out-of-Control Points**: Points beyond control limits
- **Process Capability**: Variation assessment

## Export Options

### Data Export

**Formats Available**:
- **CSV**: Comma-separated values for Excel/spreadsheets
- **JSON**: JavaScript Object Notation for programming
- **TXT**: Plain text format for reports

**Content Includes**:
- Raw data values
- Statistical calculations
- Metadata (count, processing info)

### Chart Export

**Formats Available**:
- **PNG**: High-quality image (recommended)
- **JPEG**: Compressed image format

**Features**:
- High resolution output
- Suitable for presentations
- Maintains chart quality

### Report Generation

**Comprehensive Reports Include**:
- Custom report title
- Generation timestamp
- Statistical summary
- Raw data (optional)
- Chart image (optional)

**Customization Options**:
- Include/exclude raw data
- Include/exclude statistics
- Include/exclude chart
- Custom report title

## Tips and Best Practices

### Data Preparation

1. **Clean Your Data**: Remove non-numerical values before input
2. **Check for Outliers**: Use box plots to identify extreme values
3. **Consistent Units**: Ensure all measurements use the same units
4. **Adequate Sample Size**: Use at least 30 data points for reliable statistics

### Chart Selection

1. **Histogram**: For understanding data distribution
2. **Box Plot**: For outlier detection and comparison
3. **Control Charts**: For process monitoring over time
4. **Scatter Plot**: For comparing multiple groups

### Interpretation Guidelines

**Normal Distribution**:
- Bell-shaped histogram
- Mean ≈ Median
- Skewness near 0

**Skewed Distribution**:
- Asymmetric histogram
- Mean ≠ Median
- Skewness > 0 (right skew) or < 0 (left skew)

**Process Control**:
- Points within control limits = stable process
- Points outside limits = special causes
- Trends or patterns = process changes

## Troubleshooting

### Common Issues

**Data Not Loading**:
- Check for non-numerical characters
- Verify file format (CSV)
- Ensure data is not empty

**Chart Not Displaying**:
- Verify data is loaded (check Data Summary)
- Try different plot type
- Check browser compatibility

**Export Not Working**:
- Ensure chart is generated first
- Check browser download settings
- Try different export format

### Browser Compatibility

**Supported Browsers**:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

**Required Features**:
- JavaScript enabled
- Canvas support
- File API support

### Performance Tips

**Large Datasets**:
- Use sampling for datasets > 10,000 points
- Consider data aggregation
- Use appropriate chart types

**Slow Performance**:
- Close other browser tabs
- Refresh the application
- Check system resources

## Keyboard Shortcuts

- **Ctrl/Cmd + Enter**: Generate plot
- **Ctrl/Cmd + 1**: Switch to Data Input tab
- **Ctrl/Cmd + 2**: Switch to Analysis tab
- **Ctrl/Cmd + 3**: Switch to Export tab
- **Escape**: Exit fullscreen mode

## Getting Help

### Support Resources

1. **User Guide**: This comprehensive guide
2. **Sample Data**: Built-in examples to learn from
3. **Tooltips**: Hover over controls for help
4. **Error Messages**: Descriptive error information

### Contact Information

For technical support or feature requests:
- Documentation: Check the README.md file
- Issues: Report bugs through the issue tracker
- Community: Join user discussions

## Advanced Features

### Statistical Process Control

The application supports advanced SPC features:
- **Control Chart Rules**: Automatic detection of special causes
- **Process Capability**: Assessment of process performance
- **Trend Analysis**: Identification of process changes

### Data Analysis Workflows

**Quality Control Workflow**:
1. Load measurement data
2. Create I-Chart for individual values
3. Create MR-Chart for variation
4. Analyze control patterns
5. Export control charts

**Distribution Analysis Workflow**:
1. Load sample data
2. Create histogram for shape
3. Create box plot for outliers
4. Calculate descriptive statistics
5. Export analysis report

### Integration Capabilities

The application can be integrated with:
- **Excel**: Through CSV export/import
- **Statistical Software**: Via JSON data format
- **Reporting Tools**: Through image export
- **Quality Systems**: Via data export formats
