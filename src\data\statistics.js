/**
 * Statistical calculation functions for data analysis
 */

/**
 * Calculate basic descriptive statistics for a dataset
 * @param {number[]} data - Array of numerical values
 * @returns {Object} Object containing statistical measures
 */
export function calculateDescriptiveStats(data) {
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('Data must be a non-empty array');
  }

  const validData = data.filter(val => typeof val === 'number' && !isNaN(val));
  
  if (validData.length === 0) {
    throw new Error('No valid numerical data found');
  }

  const sorted = [...validData].sort((a, b) => a - b);
  const n = validData.length;

  return {
    count: n,
    mean: calculateMean(validData),
    median: calculateMedian(sorted),
    mode: calculateMode(validData),
    standardDeviation: calculateStandardDeviation(validData),
    variance: calculateVariance(validData),
    min: Math.min(...validData),
    max: Math.max(...validData),
    range: Math.max(...validData) - Math.min(...validData),
    q1: calculateQuartile(sorted, 0.25),
    q3: calculateQuartile(sorted, 0.75),
    iqr: calculateQuartile(sorted, 0.75) - calculateQuartile(sorted, 0.25),
    skewness: calculateSkewness(validData),
    kurtosis: calculateKurtosis(validData)
  };
}

/**
 * Calculate mean (average) of dataset
 * @param {number[]} data - Array of numbers
 * @returns {number} Mean value
 */
export function calculateMean(data) {
  return data.reduce((sum, val) => sum + val, 0) / data.length;
}

/**
 * Calculate median of dataset
 * @param {number[]} sortedData - Sorted array of numbers
 * @returns {number} Median value
 */
export function calculateMedian(sortedData) {
  const n = sortedData.length;
  const mid = Math.floor(n / 2);
  
  if (n % 2 === 0) {
    return (sortedData[mid - 1] + sortedData[mid]) / 2;
  }
  return sortedData[mid];
}

/**
 * Calculate mode(s) of dataset
 * @param {number[]} data - Array of numbers
 * @returns {number[]} Array of mode values
 */
export function calculateMode(data) {
  const frequency = {};
  let maxFreq = 0;
  
  data.forEach(val => {
    frequency[val] = (frequency[val] || 0) + 1;
    maxFreq = Math.max(maxFreq, frequency[val]);
  });
  
  return Object.keys(frequency)
    .filter(val => frequency[val] === maxFreq)
    .map(Number);
}

/**
 * Calculate standard deviation
 * @param {number[]} data - Array of numbers
 * @param {boolean} sample - Whether to use sample standard deviation (default: true)
 * @returns {number} Standard deviation
 */
export function calculateStandardDeviation(data, sample = true) {
  return Math.sqrt(calculateVariance(data, sample));
}

/**
 * Calculate variance
 * @param {number[]} data - Array of numbers
 * @param {boolean} sample - Whether to use sample variance (default: true)
 * @returns {number} Variance
 */
export function calculateVariance(data, sample = true) {
  const mean = calculateMean(data);
  const squaredDiffs = data.map(val => Math.pow(val - mean, 2));
  const divisor = sample ? data.length - 1 : data.length;
  
  return squaredDiffs.reduce((sum, val) => sum + val, 0) / divisor;
}

/**
 * Calculate quartile value
 * @param {number[]} sortedData - Sorted array of numbers
 * @param {number} percentile - Percentile (0-1)
 * @returns {number} Quartile value
 */
export function calculateQuartile(sortedData, percentile) {
  const index = percentile * (sortedData.length - 1);
  const lower = Math.floor(index);
  const upper = Math.ceil(index);
  const weight = index % 1;
  
  if (upper >= sortedData.length) return sortedData[sortedData.length - 1];
  if (lower < 0) return sortedData[0];
  
  return sortedData[lower] * (1 - weight) + sortedData[upper] * weight;
}

/**
 * Calculate skewness (measure of asymmetry)
 * @param {number[]} data - Array of numbers
 * @returns {number} Skewness value
 */
export function calculateSkewness(data) {
  const mean = calculateMean(data);
  const stdDev = calculateStandardDeviation(data);
  const n = data.length;
  
  const skewSum = data.reduce((sum, val) => {
    return sum + Math.pow((val - mean) / stdDev, 3);
  }, 0);
  
  return (n / ((n - 1) * (n - 2))) * skewSum;
}

/**
 * Calculate kurtosis (measure of tail heaviness)
 * @param {number[]} data - Array of numbers
 * @returns {number} Kurtosis value
 */
export function calculateKurtosis(data) {
  const mean = calculateMean(data);
  const stdDev = calculateStandardDeviation(data);
  const n = data.length;
  
  const kurtSum = data.reduce((sum, val) => {
    return sum + Math.pow((val - mean) / stdDev, 4);
  }, 0);
  
  return ((n * (n + 1)) / ((n - 1) * (n - 2) * (n - 3))) * kurtSum - 
         (3 * Math.pow(n - 1, 2)) / ((n - 2) * (n - 3));
}

/**
 * Calculate confidence interval for mean
 * @param {number[]} data - Array of numbers
 * @param {number} confidence - Confidence level (default: 0.95)
 * @returns {Object} Confidence interval with lower and upper bounds
 */
export function calculateConfidenceInterval(data, confidence = 0.95) {
  const mean = calculateMean(data);
  const stdError = calculateStandardDeviation(data) / Math.sqrt(data.length);
  const alpha = 1 - confidence;
  const tValue = getTValue(data.length - 1, alpha / 2);
  
  const margin = tValue * stdError;
  
  return {
    lower: mean - margin,
    upper: mean + margin,
    margin: margin
  };
}

/**
 * Approximate t-value for confidence intervals (simplified)
 * @param {number} df - Degrees of freedom
 * @param {number} alpha - Alpha level
 * @returns {number} T-value
 */
function getTValue(df, alpha) {
  // More accurate t-value approximation based on confidence level
  if (alpha <= 0.025) return 2.228; // 95% confidence
  if (alpha <= 0.05) return 1.833; // 90% confidence
  if (alpha <= 0.1) return 1.372; // 80% confidence
  return 1.282; // Default
}
