/**
 * Notification Manager - Handles user notifications and alerts
 */

export class NotificationManager {
  constructor() {
    this.container = null;
    this.notifications = new Map();
    this.defaultDuration = 5000; // 5 seconds
    this.maxNotifications = 5;
    
    this.init();
  }

  /**
   * Initialize notification system
   */
  init() {
    this.container = document.getElementById('notifications');
    if (!this.container) {
      this.createContainer();
    }
  }

  /**
   * Create notifications container
   */
  createContainer() {
    this.container = document.createElement('div');
    this.container.id = 'notifications';
    this.container.className = 'notifications';
    document.body.appendChild(this.container);
  }

  /**
   * Show a notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, warning, info)
   * @param {number} duration - Duration in milliseconds (0 for persistent)
   * @param {Object} options - Additional options
   * @returns {string} Notification ID
   */
  show(message, type = 'info', duration = this.defaultDuration, options = {}) {
    const id = this.generateId();
    const notification = this.createNotification(id, message, type, options);
    
    // Add to container
    this.container.appendChild(notification);
    this.notifications.set(id, { element: notification, type, message });
    
    // Trigger entrance animation
    requestAnimationFrame(() => {
      notification.classList.add('show');
    });

    // Auto-remove if duration is set
    if (duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, duration);
    }

    // Remove oldest if too many notifications
    this.enforceMaxNotifications();

    return id;
  }

  /**
   * Show success notification
   * @param {string} message - Success message
   * @param {number} duration - Duration in milliseconds
   * @returns {string} Notification ID
   */
  success(message, duration = this.defaultDuration) {
    return this.show(message, 'success', duration);
  }

  /**
   * Show error notification
   * @param {string} message - Error message
   * @param {number} duration - Duration in milliseconds (0 for persistent)
   * @returns {string} Notification ID
   */
  error(message, duration = 0) {
    return this.show(message, 'error', duration);
  }

  /**
   * Show warning notification
   * @param {string} message - Warning message
   * @param {number} duration - Duration in milliseconds
   * @returns {string} Notification ID
   */
  warning(message, duration = this.defaultDuration) {
    return this.show(message, 'warning', duration);
  }

  /**
   * Show info notification
   * @param {string} message - Info message
   * @param {number} duration - Duration in milliseconds
   * @returns {string} Notification ID
   */
  info(message, duration = this.defaultDuration) {
    return this.show(message, 'info', duration);
  }

  /**
   * Create notification element
   * @param {string} id - Notification ID
   * @param {string} message - Notification message
   * @param {string} type - Notification type
   * @param {Object} options - Additional options
   * @returns {HTMLElement} Notification element
   */
  createNotification(id, message, type, options = {}) {
    const notification = document.createElement('div');
    notification.id = `notification-${id}`;
    notification.className = `notification ${type}`;
    notification.setAttribute('role', 'alert');
    notification.setAttribute('aria-live', 'polite');

    // Create notification content
    const content = document.createElement('div');
    content.className = 'notification-content';

    // Add icon
    const icon = document.createElement('span');
    icon.className = 'notification-icon';
    icon.innerHTML = this.getIcon(type);
    content.appendChild(icon);

    // Add message
    const messageElement = document.createElement('div');
    messageElement.className = 'notification-message';
    messageElement.textContent = message;
    content.appendChild(messageElement);

    // Add close button if not auto-dismissing
    if (options.closable !== false) {
      const closeButton = document.createElement('button');
      closeButton.className = 'notification-close';
      closeButton.innerHTML = '×';
      closeButton.setAttribute('aria-label', 'Close notification');
      closeButton.addEventListener('click', () => {
        this.remove(id);
      });
      content.appendChild(closeButton);
    }

    // Add action buttons if provided
    if (options.actions && Array.isArray(options.actions)) {
      const actionsContainer = document.createElement('div');
      actionsContainer.className = 'notification-actions';
      
      options.actions.forEach(action => {
        const button = document.createElement('button');
        button.className = `notification-action ${action.type || 'secondary'}`;
        button.textContent = action.label;
        button.addEventListener('click', () => {
          if (action.handler) {
            action.handler();
          }
          if (action.dismiss !== false) {
            this.remove(id);
          }
        });
        actionsContainer.appendChild(button);
      });
      
      content.appendChild(actionsContainer);
    }

    notification.appendChild(content);

    // Add progress bar for timed notifications
    if (options.showProgress && options.duration > 0) {
      const progressBar = document.createElement('div');
      progressBar.className = 'notification-progress';
      progressBar.style.animationDuration = `${options.duration}ms`;
      notification.appendChild(progressBar);
    }

    return notification;
  }

  /**
   * Remove notification
   * @param {string} id - Notification ID
   */
  remove(id) {
    const notificationData = this.notifications.get(id);
    if (!notificationData) return;

    const { element } = notificationData;
    
    // Add exit animation
    element.classList.add('removing');
    
    // Remove after animation
    setTimeout(() => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
      this.notifications.delete(id);
    }, 300);
  }

  /**
   * Remove all notifications
   */
  clear() {
    this.notifications.forEach((_, id) => {
      this.remove(id);
    });
  }

  /**
   * Remove notifications of specific type
   * @param {string} type - Notification type to remove
   */
  clearType(type) {
    this.notifications.forEach((notificationData, id) => {
      if (notificationData.type === type) {
        this.remove(id);
      }
    });
  }

  /**
   * Update existing notification
   * @param {string} id - Notification ID
   * @param {string} message - New message
   * @param {string} type - New type (optional)
   */
  update(id, message, type = null) {
    const notificationData = this.notifications.get(id);
    if (!notificationData) return;

    const { element } = notificationData;
    const messageElement = element.querySelector('.notification-message');
    
    if (messageElement) {
      messageElement.textContent = message;
      notificationData.message = message;
    }

    if (type && type !== notificationData.type) {
      element.className = `notification ${type}`;
      const iconElement = element.querySelector('.notification-icon');
      if (iconElement) {
        iconElement.innerHTML = this.getIcon(type);
      }
      notificationData.type = type;
    }
  }

  /**
   * Get icon for notification type
   * @param {string} type - Notification type
   * @returns {string} Icon HTML
   */
  getIcon(type) {
    const icons = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };
    return icons[type] || icons.info;
  }

  /**
   * Generate unique notification ID
   * @returns {string} Unique ID
   */
  generateId() {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Enforce maximum number of notifications
   */
  enforceMaxNotifications() {
    if (this.notifications.size <= this.maxNotifications) return;

    // Remove oldest notifications
    const notificationIds = Array.from(this.notifications.keys());
    const toRemove = notificationIds.slice(0, this.notifications.size - this.maxNotifications);
    
    toRemove.forEach(id => {
      this.remove(id);
    });
  }

  /**
   * Show confirmation dialog
   * @param {string} message - Confirmation message
   * @param {Object} options - Dialog options
   * @returns {Promise<boolean>} User's choice
   */
  confirm(message, options = {}) {
    return new Promise((resolve) => {
      const id = this.show(message, 'warning', 0, {
        closable: false,
        actions: [
          {
            label: options.confirmLabel || 'Confirm',
            type: 'primary',
            handler: () => resolve(true)
          },
          {
            label: options.cancelLabel || 'Cancel',
            type: 'secondary',
            handler: () => resolve(false)
          }
        ]
      });
    });
  }

  /**
   * Show loading notification
   * @param {string} message - Loading message
   * @returns {string} Notification ID
   */
  loading(message = 'Loading...') {
    return this.show(message, 'info', 0, {
      closable: false,
      showProgress: false
    });
  }

  /**
   * Update loading notification
   * @param {string} id - Notification ID
   * @param {string} message - New loading message
   */
  updateLoading(id, message) {
    this.update(id, message);
  }

  /**
   * Complete loading notification
   * @param {string} id - Notification ID
   * @param {string} message - Completion message
   * @param {string} type - Final notification type
   */
  completeLoading(id, message, type = 'success') {
    this.update(id, message, type);
    setTimeout(() => {
      this.remove(id);
    }, 2000);
  }

  /**
   * Show progress notification
   * @param {string} message - Progress message
   * @param {number} progress - Progress percentage (0-100)
   * @returns {string} Notification ID
   */
  progress(message, progress = 0) {
    const id = this.generateId();
    const notification = this.createProgressNotification(id, message, progress);
    
    this.container.appendChild(notification);
    this.notifications.set(id, { element: notification, type: 'progress', message });
    
    requestAnimationFrame(() => {
      notification.classList.add('show');
    });

    return id;
  }

  /**
   * Update progress notification
   * @param {string} id - Notification ID
   * @param {string} message - Progress message
   * @param {number} progress - Progress percentage (0-100)
   */
  updateProgress(id, message, progress) {
    const notificationData = this.notifications.get(id);
    if (!notificationData) return;

    const { element } = notificationData;
    const messageElement = element.querySelector('.notification-message');
    const progressBar = element.querySelector('.progress-fill');
    const progressText = element.querySelector('.progress-text');
    
    if (messageElement) {
      messageElement.textContent = message;
    }
    
    if (progressBar) {
      progressBar.style.width = `${Math.max(0, Math.min(100, progress))}%`;
    }
    
    if (progressText) {
      progressText.textContent = `${Math.round(progress)}%`;
    }
  }

  /**
   * Create progress notification element
   * @param {string} id - Notification ID
   * @param {string} message - Progress message
   * @param {number} progress - Initial progress
   * @returns {HTMLElement} Notification element
   */
  createProgressNotification(id, message, progress) {
    const notification = document.createElement('div');
    notification.id = `notification-${id}`;
    notification.className = 'notification progress';
    
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-message">${message}</div>
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${progress}%"></div>
          </div>
          <div class="progress-text">${Math.round(progress)}%</div>
        </div>
      </div>
    `;
    
    return notification;
  }

  /**
   * Get notification count by type
   * @param {string} type - Notification type
   * @returns {number} Count of notifications
   */
  getCount(type = null) {
    if (!type) {
      return this.notifications.size;
    }
    
    let count = 0;
    this.notifications.forEach(notificationData => {
      if (notificationData.type === type) {
        count++;
      }
    });
    
    return count;
  }

  /**
   * Check if notification exists
   * @param {string} id - Notification ID
   * @returns {boolean} Whether notification exists
   */
  exists(id) {
    return this.notifications.has(id);
  }
}
