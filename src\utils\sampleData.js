/**
 * Sample Data Generator - Creates various types of sample datasets for testing
 */

export class SampleDataGenerator {
  constructor() {
    this.random = Math.random;
  }

  /**
   * Generate normal distribution data using Box-Muller transform
   * @param {number} count - Number of data points
   * @param {number} mean - Mean of the distribution
   * @param {number} stdDev - Standard deviation
   * @returns {number[]} Array of normally distributed values
   */
  generateNormalDistribution(count = 50, mean = 100, stdDev = 15) {
    const data = [];
    
    for (let i = 0; i < count; i += 2) {
      // Box-Muller transform
      const u1 = this.random();
      const u2 = this.random();
      
      const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
      const z1 = Math.sqrt(-2 * Math.log(u1)) * Math.sin(2 * Math.PI * u2);
      
      data.push(z0 * stdDev + mean);
      if (data.length < count) {
        data.push(z1 * stdDev + mean);
      }
    }
    
    return data.slice(0, count);
  }

  /**
   * Generate skewed data (right-skewed using exponential distribution)
   * @param {number} count - Number of data points
   * @param {number} lambda - Rate parameter for exponential distribution
   * @returns {number[]} Array of skewed values
   */
  generateSkewedData(count = 40, lambda = 0.5) {
    const data = [];
    
    for (let i = 0; i < count; i++) {
      // Exponential distribution: -ln(1-u)/lambda
      const u = this.random();
      const value = -Math.log(1 - u) / lambda;
      data.push(value);
    }
    
    return data;
  }

  /**
   * Generate grouped data with different means
   * @param {Object} options - Generation options
   * @returns {Object} Object with data and groups arrays
   */
  generateGroupedData(options = {}) {
    const {
      groups = ['A', 'B', 'C'],
      pointsPerGroup = 15,
      baseMean = 50,
      meanDifference = 10,
      stdDev = 5
    } = options;

    const data = [];
    const groupLabels = [];

    groups.forEach((group, index) => {
      const groupMean = baseMean + (index * meanDifference);
      const groupData = this.generateNormalDistribution(pointsPerGroup, groupMean, stdDev);
      
      data.push(...groupData);
      groupLabels.push(...Array(pointsPerGroup).fill(group));
    });

    return {
      data,
      groups: groupLabels
    };
  }

  /**
   * Generate data with outliers
   * @param {number} count - Number of data points
   * @param {number} outlierPercent - Percentage of outliers (0-100)
   * @returns {number[]} Array with outliers
   */
  generateDataWithOutliers(count = 30, outlierPercent = 10) {
    const normalCount = Math.floor(count * (100 - outlierPercent) / 100);
    const outlierCount = count - normalCount;
    
    // Generate normal data
    const normalData = this.generateNormalDistribution(normalCount, 50, 8);
    
    // Generate outliers (far from the mean)
    const outliers = [];
    for (let i = 0; i < outlierCount; i++) {
      const isHighOutlier = this.random() > 0.5;
      const outlier = isHighOutlier ? 
        50 + 30 + (this.random() * 20) : // High outlier
        50 - 30 - (this.random() * 20);  // Low outlier
      outliers.push(outlier);
    }
    
    // Combine and shuffle
    const allData = [...normalData, ...outliers];
    return this.shuffleArray(allData);
  }

  /**
   * Generate time series data with trend
   * @param {number} count - Number of data points
   * @param {Object} options - Generation options
   * @returns {Object} Object with data, timestamps, and trend info
   */
  generateTimeSeriesData(count = 50, options = {}) {
    const {
      startValue = 100,
      trend = 0.5, // Units per time step
      seasonality = 5, // Amplitude of seasonal component
      noise = 2, // Standard deviation of noise
      seasonalPeriod = 12 // Period of seasonal component
    } = options;

    const data = [];
    const timestamps = [];
    const startDate = new Date();

    for (let i = 0; i < count; i++) {
      // Linear trend
      const trendComponent = startValue + (trend * i);
      
      // Seasonal component
      const seasonalComponent = seasonality * Math.sin(2 * Math.PI * i / seasonalPeriod);
      
      // Random noise
      const noiseComponent = this.generateNormalDistribution(1, 0, noise)[0];
      
      // Combine components
      const value = trendComponent + seasonalComponent + noiseComponent;
      data.push(value);
      
      // Generate timestamp (daily intervals)
      const timestamp = new Date(startDate);
      timestamp.setDate(startDate.getDate() + i);
      timestamps.push(timestamp);
    }

    return {
      data,
      timestamps,
      trend: {
        slope: trend,
        intercept: startValue,
        seasonalAmplitude: seasonality,
        period: seasonalPeriod
      }
    };
  }

  /**
   * Generate bimodal distribution data
   * @param {number} count - Number of data points
   * @param {Object} options - Generation options
   * @returns {number[]} Array of bimodal values
   */
  generateBimodalData(count = 60, options = {}) {
    const {
      mean1 = 30,
      mean2 = 70,
      stdDev1 = 8,
      stdDev2 = 8,
      proportion1 = 0.5 // Proportion of data from first distribution
    } = options;

    const data = [];
    const count1 = Math.floor(count * proportion1);
    const count2 = count - count1;

    // Generate data from first distribution
    const data1 = this.generateNormalDistribution(count1, mean1, stdDev1);
    
    // Generate data from second distribution
    const data2 = this.generateNormalDistribution(count2, mean2, stdDev2);

    // Combine and shuffle
    data.push(...data1, ...data2);
    return this.shuffleArray(data);
  }

  /**
   * Generate uniform distribution data
   * @param {number} count - Number of data points
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @returns {number[]} Array of uniformly distributed values
   */
  generateUniformData(count = 40, min = 0, max = 100) {
    const data = [];
    
    for (let i = 0; i < count; i++) {
      const value = min + (this.random() * (max - min));
      data.push(value);
    }
    
    return data;
  }

  /**
   * Generate control chart data (for process control)
   * @param {number} count - Number of data points
   * @param {Object} options - Generation options
   * @returns {Object} Control chart data with special causes
   */
  generateControlChartData(count = 100, options = {}) {
    const {
      targetMean = 50,
      processStdDev = 2,
      shiftPoint = null, // Point where process shifts
      shiftMagnitude = 5, // Magnitude of process shift
      trendStart = null, // Point where trend starts
      trendRate = 0.1 // Rate of trend per point
    } = options;

    const data = [];
    const specialCauses = [];

    for (let i = 0; i < count; i++) {
      let mean = targetMean;
      
      // Apply process shift
      if (shiftPoint !== null && i >= shiftPoint) {
        mean += shiftMagnitude;
        if (i === shiftPoint) {
          specialCauses.push({ point: i, type: 'shift', magnitude: shiftMagnitude });
        }
      }
      
      // Apply trend
      if (trendStart !== null && i >= trendStart) {
        mean += (i - trendStart) * trendRate;
        if (i === trendStart) {
          specialCauses.push({ point: i, type: 'trend', rate: trendRate });
        }
      }
      
      // Generate point with some random variation
      const value = this.generateNormalDistribution(1, mean, processStdDev)[0];
      data.push(value);
    }

    return {
      data,
      specialCauses,
      controlLimits: {
        ucl: targetMean + 3 * processStdDev,
        lcl: targetMean - 3 * processStdDev,
        centerLine: targetMean
      }
    };
  }

  /**
   * Generate measurement system analysis data
   * @param {number} parts - Number of parts
   * @param {number} operators - Number of operators
   * @param {number} trials - Number of trials per part-operator combination
   * @returns {Object} MSA data structure
   */
  generateMSAData(parts = 10, operators = 3, trials = 2) {
    const data = [];
    const partNames = Array.from({ length: parts }, (_, i) => `Part_${i + 1}`);
    const operatorNames = Array.from({ length: operators }, (_, i) => `Operator_${String.fromCharCode(65 + i)}`);
    
    // Generate true part values
    const truePartValues = this.generateNormalDistribution(parts, 50, 10);
    
    // Generate operator bias
    const operatorBias = this.generateNormalDistribution(operators, 0, 1);
    
    // Measurement error standard deviation
    const measurementError = 0.5;

    partNames.forEach((part, partIndex) => {
      operatorNames.forEach((operator, operatorIndex) => {
        for (let trial = 1; trial <= trials; trial++) {
          const trueValue = truePartValues[partIndex];
          const bias = operatorBias[operatorIndex];
          const error = this.generateNormalDistribution(1, 0, measurementError)[0];
          
          const measurement = trueValue + bias + error;
          
          data.push({
            part,
            operator,
            trial,
            measurement,
            trueValue
          });
        }
      });
    });

    return {
      data,
      partNames,
      operatorNames,
      truePartValues,
      operatorBias,
      measurementError
    };
  }

  /**
   * Shuffle array using Fisher-Yates algorithm
   * @param {Array} array - Array to shuffle
   * @returns {Array} Shuffled array
   */
  shuffleArray(array) {
    const shuffled = [...array];
    
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(this.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    
    return shuffled;
  }

  /**
   * Get sample dataset by name
   * @param {string} name - Name of sample dataset
   * @param {Object} options - Generation options
   * @returns {Object} Sample dataset
   */
  getSampleDataset(name, options = {}) {
    const datasets = {
      'quality_measurements': () => ({
        data: this.generateNormalDistribution(50, 100, 5),
        description: 'Quality measurements from a manufacturing process',
        units: 'mm'
      }),
      
      'customer_satisfaction': () => ({
        data: this.generateSkewedData(100, 0.3).map(x => Math.min(10, Math.max(1, Math.round(x + 1)))),
        description: 'Customer satisfaction ratings (1-10 scale)',
        units: 'rating'
      }),
      
      'sales_by_region': () => {
        const grouped = this.generateGroupedData({
          groups: ['North', 'South', 'East', 'West'],
          pointsPerGroup: 12,
          baseMean: 1000,
          meanDifference: 200,
          stdDev: 150
        });
        return {
          data: grouped.data,
          groups: grouped.groups,
          description: 'Monthly sales data by region',
          units: 'thousands USD'
        };
      },
      
      'process_control': () => {
        const controlData = this.generateControlChartData(100, {
          shiftPoint: 60,
          shiftMagnitude: 3
        });
        return {
          data: controlData.data,
          description: 'Process control measurements with a shift at point 60',
          units: 'units',
          specialCauses: controlData.specialCauses
        };
      },
      
      'temperature_readings': () => {
        const timeSeriesData = this.generateTimeSeriesData(365, {
          startValue: 20,
          trend: 0.01,
          seasonality: 15,
          seasonalPeriod: 365 / 4,
          noise: 3
        });
        return {
          data: timeSeriesData.data,
          timestamps: timeSeriesData.timestamps,
          description: 'Daily temperature readings over one year',
          units: '°C'
        };
      }
    };

    const generator = datasets[name];
    if (!generator) {
      throw new Error(`Unknown sample dataset: ${name}`);
    }

    return generator();
  }

  /**
   * Get list of available sample datasets
   * @returns {Array} Array of dataset names and descriptions
   */
  getAvailableDatasets() {
    return [
      { name: 'quality_measurements', description: 'Quality measurements from manufacturing' },
      { name: 'customer_satisfaction', description: 'Customer satisfaction ratings' },
      { name: 'sales_by_region', description: 'Sales data grouped by region' },
      { name: 'process_control', description: 'Process control data with special causes' },
      { name: 'temperature_readings', description: 'Time series temperature data' }
    ];
  }
}
