/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Palette */
  --primary-color: #3498db;
  --primary-dark: #2980b9;
  --secondary-color: #95a5a6;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --error-color: #e74c3c;
  --info-color: #17a2b8;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8f9fa;
  --gray-100: #e9ecef;
  --gray-200: #dee2e6;
  --gray-300: #ced4da;
  --gray-400: #adb5bd;
  --gray-500: #6c757d;
  --gray-600: #495057;
  --gray-700: #343a40;
  --gray-800: #212529;
  --gray-900: #0d1117;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-700);
  background-color: var(--gray-50);
  min-height: 100vh;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Header */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
}

.logo-icon {
  font-size: var(--font-size-2xl);
}

/* Navigation */
.nav {
  display: flex;
  gap: var(--spacing-2);
}

.nav-btn {
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--gray-300);
  background: var(--white);
  color: var(--gray-600);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.nav-btn:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.nav-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

/* Main Content */
.main {
  flex: 1;
  padding: var(--spacing-8) 0;
}

/* Tabs */
.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Cards */
.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 2px solid var(--gray-100);
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  color: var(--gray-700);
}

.form-control {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
  background: var(--white);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control::placeholder {
  color: var(--gray-400);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 40px;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
}

.btn-icon {
  padding: var(--spacing-2);
  min-width: 36px;
  min-height: 36px;
}

.btn-full {
  width: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Input Methods */
.input-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

.input-method {
  padding: var(--spacing-4);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  background: var(--gray-50);
}

.input-method h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
}

/* File Upload */
.file-upload {
  position: relative;
  margin-bottom: var(--spacing-4);
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-md);
  background: var(--white);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.file-label:hover {
  border-color: var(--primary-color);
  background: var(--gray-50);
}

.file-icon {
  font-size: var(--font-size-lg);
}

/* Sample Data Buttons */
.sample-data-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--spacing-2);
}

/* Data Summary */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-4);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.summary-label {
  font-weight: 500;
  color: var(--gray-600);
}

.summary-value {
  font-weight: 600;
  color: var(--gray-800);
}

/* Analysis Layout */
.analysis-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--spacing-6);
}

@media (max-width: 1024px) {
  .analysis-layout {
    grid-template-columns: 1fr;
  }
}

/* Controls Panel */
.controls-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  cursor: pointer;
  font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-size: var(--font-size-xs);
  font-weight: bold;
}

/* Chart Area */
.chart-card {
  min-height: 500px;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--gray-200);
}

.chart-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.chart-actions {
  display: flex;
  gap: var(--spacing-2);
}

.chart-container {
  position: relative;
  height: 400px;
}

#main-chart {
  width: 100% !important;
  height: 100% !important;
  display: none; /* Initially hidden */
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--gray-50);
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-md);
}

.placeholder-content {
  text-align: center;
  color: var(--gray-500);
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-3);
  display: block;
}

.placeholder-content h4 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-2);
}

/* Statistics Content */
.statistics-content {
  max-height: 300px;
  overflow-y: auto;
}

.no-data {
  text-align: center;
  color: var(--gray-500);
  font-style: italic;
  padding: var(--spacing-4);
}

/* Export Options */
.export-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.export-section h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
}

.export-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
}

/* Footer */
.footer {
  background: var(--gray-800);
  color: var(--gray-300);
  text-align: center;
  padding: var(--spacing-4) 0;
  margin-top: auto;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mt-4 {
  margin-top: var(--spacing-4);
}

.mb-4 {
  margin-bottom: var(--spacing-4);
}

.p-4 {
  padding: var(--spacing-4);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  background: var(--white);
  padding: var(--spacing-8);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-xl);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notifications */
.notifications {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.notification {
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  animation: slideIn 0.3s ease-out;
  opacity: 0;
  transform: translateX(100%);
}

.notification.show {
  opacity: 1;
  transform: translateX(0);
}

.notification.removing {
  animation: slideOut 0.3s ease-in forwards;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.notification-icon {
  font-size: var(--font-size-lg);
  font-weight: bold;
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-weight: 500;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: 0;
  margin-left: var(--spacing-2);
  opacity: 0.8;
  transition: opacity var(--transition-fast);
}

.notification-close:hover {
  opacity: 1;
}

.notification-actions {
  display: flex;
  gap: var(--spacing-2);
  margin-top: var(--spacing-3);
}

.notification-action {
  padding: var(--spacing-1) var(--spacing-3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.notification-action:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-action.primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  animation: progressBar linear;
}

.notification.success {
  background: var(--success-color);
  color: var(--white);
}

.notification.error {
  background: var(--error-color);
  color: var(--white);
}

.notification.warning {
  background: var(--warning-color);
  color: var(--white);
}

.notification.info {
  background: var(--info-color);
  color: var(--white);
}

.notification.progress {
  background: var(--primary-color);
  color: var(--white);
}

.progress-container {
  margin-top: var(--spacing-2);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--font-size-sm);
  text-align: center;
  margin-top: var(--spacing-1);
  opacity: 0.9;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Form Validation */
.form-control.valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1);
}

.form-control.invalid {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.validation-message {
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
  padding: var(--spacing-1) 0;
}

.validation-message.valid {
  color: var(--success-color);
}

.validation-message.invalid {
  color: var(--error-color);
}

/* CSV Preview */
.csv-preview {
  margin-top: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--white);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.csv-preview h4 {
  margin-bottom: var(--spacing-3);
  color: var(--gray-700);
  font-size: var(--font-size-lg);
}

.csv-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.csv-table th,
.csv-table td {
  padding: var(--spacing-2);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.csv-table th {
  background: var(--gray-50);
  font-weight: 600;
  color: var(--gray-700);
}

.csv-table td {
  color: var(--gray-600);
}

.csv-table tr:hover {
  background: var(--gray-50);
}

/* Loading States */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: inherit;
}

/* Progress Bar */
.progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gray-200);
  z-index: 1000;
}

.progress-track {
  width: 100%;
  height: 100%;
  background: var(--gray-200);
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width var(--transition-normal);
  width: 0%;
}

.progress-text {
  position: fixed;
  top: var(--spacing-2);
  left: 50%;
  transform: translateX(-50%);
  background: var(--white);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  z-index: 1001;
}

/* Animations */
.animate {
  animation-duration: 0.3s;
  animation-fill-mode: both;
}

.fadeIn {
  animation-name: fadeIn;
}

.fadeOut {
  animation-name: fadeOut;
}

.slideInUp {
  animation-name: slideInUp;
}

.slideOutDown {
  animation-name: slideOutDown;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(20px);
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .nav {
    width: 100%;
    justify-content: center;
  }

  .input-methods {
    grid-template-columns: 1fr;
  }

  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .export-buttons {
    flex-direction: column;
  }

  .chart-container {
    height: 300px;
  }

  .notifications {
    left: var(--spacing-2);
    right: var(--spacing-2);
    top: var(--spacing-2);
  }

  .notification {
    max-width: none;
  }
}
