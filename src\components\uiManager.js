/**
 * UI Manager - Handles UI interactions and state management
 */

export class UIManager {
  constructor() {
    this.activeTab = 'analysis';
    this.isFullscreen = false;
  }

  /**
   * Initialize UI components
   */
  init() {
    this.setupResponsiveHandlers();
    this.setupKeyboardShortcuts();
    this.setupTooltips();
    this.initializeFormValidation();
  }

  /**
   * Setup responsive behavior handlers
   */
  setupResponsiveHandlers() {
    // Handle window resize
    window.addEventListener('resize', () => {
      this.handleResize();
    });

    // Handle orientation change on mobile
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.handleResize(), 100);
    });
  }

  /**
   * Handle window resize events
   */
  handleResize() {
    // Trigger chart resize if charts exist
    const event = new CustomEvent('chartResize');
    document.dispatchEvent(event);
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + Enter to generate plot
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        const generateBtn = document.getElementById('generate-plot');
        if (generateBtn && !generateBtn.disabled) {
          generateBtn.click();
        }
      }

      // Escape to exit fullscreen
      if (e.key === 'Escape' && this.isFullscreen) {
        document.exitFullscreen();
      }

      // Tab navigation with Ctrl + number
      if (e.ctrlKey && e.key >= '1' && e.key <= '3') {
        e.preventDefault();
        const tabs = ['data', 'analysis', 'export'];
        const tabIndex = parseInt(e.key) - 1;
        if (tabs[tabIndex]) {
          this.switchToTab(tabs[tabIndex]);
        }
      }
    });
  }

  /**
   * Switch to specific tab
   * @param {string} tabName - Name of tab to switch to
   */
  switchToTab(tabName) {
    const tabButton = document.querySelector(`[data-tab="${tabName}"]`);
    if (tabButton) {
      tabButton.click();
    }
  }

  /**
   * Setup tooltips for UI elements
   */
  setupTooltips() {
    // Add tooltips to buttons and form elements
    const tooltipElements = [
      { selector: '#show-mean', text: 'Display the mean line on the chart' },
      { selector: '#show-confidence', text: 'Show confidence interval bands' },
      { selector: '#show-grid', text: 'Display grid lines for easier reading' },
      { selector: '#show-legend', text: 'Show chart legend' },
      { selector: '#export-chart', text: 'Export chart as PNG image' },
      { selector: '#fullscreen-chart', text: 'View chart in fullscreen mode' }
    ];

    tooltipElements.forEach(({ selector, text }) => {
      const element = document.querySelector(selector);
      if (element) {
        element.title = text;
      }
    });
  }

  /**
   * Initialize form validation
   */
  initializeFormValidation() {
    // Real-time validation for manual data input
    const manualDataInput = document.getElementById('manual-data');
    if (manualDataInput) {
      manualDataInput.addEventListener('input', (e) => {
        this.validateDataInput(e.target);
      });
    }

    // Validation for group data
    const groupDataInput = document.getElementById('group-data');
    if (groupDataInput) {
      groupDataInput.addEventListener('input', (e) => {
        this.validateGroupInput(e.target);
      });
    }

    // Chart title validation
    const chartTitleInput = document.getElementById('chart-title');
    if (chartTitleInput) {
      chartTitleInput.addEventListener('input', (e) => {
        this.validateChartTitle(e.target);
      });
    }
  }

  /**
   * Validate data input
   * @param {HTMLElement} input - Input element to validate
   */
  validateDataInput(input) {
    const value = input.value.trim();
    const isValid = this.isValidDataInput(value);
    
    this.setInputValidation(input, isValid, 
      isValid ? '' : 'Please enter numbers separated by spaces or commas');
  }

  /**
   * Validate group input
   * @param {HTMLElement} input - Input element to validate
   */
  validateGroupInput(input) {
    const value = input.value.trim();
    const dataInput = document.getElementById('manual-data').value.trim();
    
    if (!value) {
      this.setInputValidation(input, true, '');
      return;
    }

    if (!dataInput) {
      this.setInputValidation(input, false, 'Please enter data first');
      return;
    }

    try {
      const dataCount = dataInput.split(/[,;\s\t]+/).filter(v => v.trim()).length;
      const groupCount = value.split(/[,;\s\t]+/).filter(v => v.trim()).length;
      const isValid = dataCount === groupCount;
      
      this.setInputValidation(input, isValid, 
        isValid ? '' : `Number of groups (${groupCount}) must match data points (${dataCount})`);
    } catch (error) {
      this.setInputValidation(input, false, 'Invalid group format');
    }
  }

  /**
   * Validate chart title
   * @param {HTMLElement} input - Input element to validate
   */
  validateChartTitle(input) {
    const value = input.value.trim();
    const isValid = value.length <= 100;
    
    this.setInputValidation(input, isValid, 
      isValid ? '' : 'Chart title must be 100 characters or less');
  }

  /**
   * Check if data input is valid
   * @param {string} value - Input value to check
   * @returns {boolean} Whether input is valid
   */
  isValidDataInput(value) {
    if (!value) return false;
    
    try {
      const numbers = value.split(/[,;\s\t]+/)
        .filter(v => v.trim())
        .map(v => parseFloat(v.trim()));
      
      return numbers.length > 0 && numbers.every(n => !isNaN(n));
    } catch (error) {
      return false;
    }
  }

  /**
   * Set input validation state
   * @param {HTMLElement} input - Input element
   * @param {boolean} isValid - Whether input is valid
   * @param {string} message - Validation message
   */
  setInputValidation(input, isValid, message) {
    // Remove existing validation classes
    input.classList.remove('valid', 'invalid');
    
    // Add appropriate class
    if (input.value.trim()) {
      input.classList.add(isValid ? 'valid' : 'invalid');
    }

    // Handle validation message
    let messageElement = input.parentNode.querySelector('.validation-message');
    
    if (message) {
      if (!messageElement) {
        messageElement = document.createElement('div');
        messageElement.className = 'validation-message';
        input.parentNode.appendChild(messageElement);
      }
      messageElement.textContent = message;
      messageElement.className = `validation-message ${isValid ? 'valid' : 'invalid'}`;
    } else if (messageElement) {
      messageElement.remove();
    }
  }

  /**
   * Show/hide loading state for specific element
   * @param {string} elementId - ID of element to update
   * @param {boolean} loading - Whether to show loading state
   */
  setElementLoading(elementId, loading) {
    const element = document.getElementById(elementId);
    if (!element) return;

    if (loading) {
      element.disabled = true;
      element.classList.add('loading');
      
      // Store original text and show loading text
      if (!element.dataset.originalText) {
        element.dataset.originalText = element.textContent;
      }
      element.textContent = 'Loading...';
    } else {
      element.disabled = false;
      element.classList.remove('loading');
      
      // Restore original text
      if (element.dataset.originalText) {
        element.textContent = element.dataset.originalText;
        delete element.dataset.originalText;
      }
    }
  }

  /**
   * Update progress indicator
   * @param {number} progress - Progress percentage (0-100)
   * @param {string} message - Progress message
   */
  updateProgress(progress, message = '') {
    let progressBar = document.getElementById('progress-bar');
    
    if (!progressBar) {
      // Create progress bar if it doesn't exist
      progressBar = this.createProgressBar();
    }

    const progressFill = progressBar.querySelector('.progress-fill');
    const progressText = progressBar.querySelector('.progress-text');
    
    progressFill.style.width = `${Math.max(0, Math.min(100, progress))}%`;
    progressText.textContent = message;
    
    // Show/hide progress bar
    progressBar.classList.toggle('hidden', progress <= 0 || progress >= 100);
  }

  /**
   * Create progress bar element
   * @returns {HTMLElement} Progress bar element
   */
  createProgressBar() {
    const progressBar = document.createElement('div');
    progressBar.id = 'progress-bar';
    progressBar.className = 'progress-bar hidden';
    progressBar.innerHTML = `
      <div class="progress-track">
        <div class="progress-fill"></div>
      </div>
      <div class="progress-text"></div>
    `;
    
    document.body.appendChild(progressBar);
    return progressBar;
  }

  /**
   * Animate element entrance
   * @param {HTMLElement} element - Element to animate
   * @param {string} animation - Animation type
   */
  animateIn(element, animation = 'fadeIn') {
    element.classList.add('animate', animation);
    
    element.addEventListener('animationend', () => {
      element.classList.remove('animate', animation);
    }, { once: true });
  }

  /**
   * Animate element exit
   * @param {HTMLElement} element - Element to animate
   * @param {string} animation - Animation type
   * @returns {Promise} Promise that resolves when animation completes
   */
  animateOut(element, animation = 'fadeOut') {
    return new Promise((resolve) => {
      element.classList.add('animate', animation);
      
      element.addEventListener('animationend', () => {
        element.classList.remove('animate', animation);
        resolve();
      }, { once: true });
    });
  }

  /**
   * Smooth scroll to element
   * @param {string} elementId - ID of element to scroll to
   * @param {number} offset - Offset from top in pixels
   */
  scrollToElement(elementId, offset = 0) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const elementTop = element.offsetTop - offset;
    window.scrollTo({
      top: elementTop,
      behavior: 'smooth'
    });
  }

  /**
   * Copy text to clipboard
   * @param {string} text - Text to copy
   * @returns {Promise<boolean>} Success status
   */
  async copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      
      try {
        document.execCommand('copy');
        return true;
      } catch (fallbackError) {
        return false;
      } finally {
        document.body.removeChild(textArea);
      }
    }
  }

  /**
   * Format number for display
   * @param {number} value - Number to format
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted number
   */
  formatNumber(value, decimals = 3) {
    if (typeof value !== 'number' || isNaN(value)) {
      return 'N/A';
    }
    
    return value.toFixed(decimals);
  }

  /**
   * Debounce function calls
   * @param {Function} func - Function to debounce
   * @param {number} wait - Wait time in milliseconds
   * @returns {Function} Debounced function
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Throttle function calls
   * @param {Function} func - Function to throttle
   * @param {number} limit - Time limit in milliseconds
   * @returns {Function} Throttled function
   */
  throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}
