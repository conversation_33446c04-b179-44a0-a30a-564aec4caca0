/**
 * File Manager - Handles file operations and downloads
 */

export class FileManager {
  constructor() {
    this.supportedFormats = {
      csv: 'text/csv',
      json: 'application/json',
      txt: 'text/plain',
      png: 'image/png',
      jpeg: 'image/jpeg',
      jpg: 'image/jpeg'
    };
  }

  /**
   * Read file as text
   * @param {File} file - File to read
   * @returns {Promise<string>} File content as text
   */
  readFileAsText(file) {
    return new Promise((resolve, reject) => {
      if (!file) {
        reject(new Error('No file provided'));
        return;
      }

      const reader = new FileReader();
      
      reader.onload = (event) => {
        resolve(event.target.result);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsText(file);
    });
  }

  /**
   * Read file as data URL
   * @param {File} file - File to read
   * @returns {Promise<string>} File content as data URL
   */
  readFileAsDataURL(file) {
    return new Promise((resolve, reject) => {
      if (!file) {
        reject(new Error('No file provided'));
        return;
      }

      const reader = new FileReader();
      
      reader.onload = (event) => {
        resolve(event.target.result);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsDataURL(file);
    });
  }

  /**
   * Download file with given content
   * @param {string} content - File content
   * @param {string} filename - Name of the file
   * @param {string} mimeType - MIME type of the file
   */
  downloadFile(content, filename, mimeType = 'text/plain') {
    try {
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the URL object
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 100);
      
    } catch (error) {
      throw new Error(`Failed to download file: ${error.message}`);
    }
  }

  /**
   * Download image from data URL
   * @param {string} dataURL - Data URL of the image
   * @param {string} filename - Name of the file
   */
  downloadImageFromDataURL(dataURL, filename) {
    try {
      const link = document.createElement('a');
      link.href = dataURL;
      link.download = filename;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
    } catch (error) {
      throw new Error(`Failed to download image: ${error.message}`);
    }
  }

  /**
   * Download blob as file
   * @param {Blob} blob - Blob to download
   * @param {string} filename - Name of the file
   */
  downloadBlob(blob, filename) {
    try {
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 100);
      
    } catch (error) {
      throw new Error(`Failed to download blob: ${error.message}`);
    }
  }

  /**
   * Validate file type
   * @param {File} file - File to validate
   * @param {string[]} allowedTypes - Array of allowed MIME types
   * @returns {boolean} Whether file type is valid
   */
  validateFileType(file, allowedTypes) {
    if (!file) return false;
    return allowedTypes.includes(file.type);
  }

  /**
   * Validate file size
   * @param {File} file - File to validate
   * @param {number} maxSizeBytes - Maximum file size in bytes
   * @returns {boolean} Whether file size is valid
   */
  validateFileSize(file, maxSizeBytes) {
    if (!file) return false;
    return file.size <= maxSizeBytes;
  }

  /**
   * Get file extension from filename
   * @param {string} filename - Filename
   * @returns {string} File extension (without dot)
   */
  getFileExtension(filename) {
    if (!filename) return '';
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
  }

  /**
   * Get MIME type from file extension
   * @param {string} extension - File extension
   * @returns {string} MIME type
   */
  getMimeType(extension) {
    return this.supportedFormats[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Create CSV content from data
   * @param {Array} headers - CSV headers
   * @param {Array[]} rows - CSV rows
   * @returns {string} CSV content
   */
  createCSV(headers, rows) {
    const csvContent = [];
    
    // Add headers
    if (headers && headers.length > 0) {
      csvContent.push(headers.map(header => this.escapeCSVField(header)).join(','));
    }
    
    // Add rows
    rows.forEach(row => {
      const csvRow = row.map(field => this.escapeCSVField(field)).join(',');
      csvContent.push(csvRow);
    });
    
    return csvContent.join('\n');
  }

  /**
   * Escape CSV field
   * @param {any} field - Field value
   * @returns {string} Escaped field
   */
  escapeCSVField(field) {
    if (field === null || field === undefined) {
      return '';
    }
    
    const stringField = String(field);
    
    // If field contains comma, newline, or quote, wrap in quotes and escape quotes
    if (stringField.includes(',') || stringField.includes('\n') || stringField.includes('"')) {
      return '"' + stringField.replace(/"/g, '""') + '"';
    }
    
    return stringField;
  }

  /**
   * Parse CSV content
   * @param {string} csvContent - CSV content to parse
   * @returns {Object} Parsed CSV data with headers and rows
   */
  parseCSV(csvContent) {
    if (!csvContent || csvContent.trim() === '') {
      throw new Error('CSV content is empty');
    }

    const lines = csvContent.trim().split('\n');
    if (lines.length === 0 || (lines.length === 1 && lines[0].trim() === '')) {
      throw new Error('CSV content is empty');
    }

    const result = {
      headers: [],
      rows: []
    };

    // Parse first line as headers
    result.headers = this.parseCSVLine(lines[0]);

    // Parse remaining lines as data rows
    for (let i = 1; i < lines.length; i++) {
      const row = this.parseCSVLine(lines[i]);
      if (row.length > 0) {
        result.rows.push(row);
      }
    }

    return result;
  }

  /**
   * Parse single CSV line
   * @param {string} line - CSV line to parse
   * @returns {string[]} Parsed fields
   */
  parseCSVLine(line) {
    const fields = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];
      const nextChar = line[i + 1];

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        // Field separator
        fields.push(current.trim());
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    // Add the last field
    fields.push(current.trim());

    return fields;
  }

  /**
   * Create JSON file content
   * @param {any} data - Data to convert to JSON
   * @param {boolean} pretty - Whether to format JSON prettily
   * @returns {string} JSON content
   */
  createJSON(data, pretty = true) {
    try {
      return pretty ? JSON.stringify(data, null, 2) : JSON.stringify(data);
    } catch (error) {
      throw new Error(`Failed to create JSON: ${error.message}`);
    }
  }

  /**
   * Create text report
   * @param {Object} data - Data for the report
   * @param {Object} options - Report options
   * @returns {string} Text report content
   */
  createTextReport(data, options = {}) {
    const {
      title = 'Data Analysis Report',
      includeTimestamp = true,
      includeStatistics = true,
      includeRawData = false
    } = options;

    let report = '';

    // Title
    report += title + '\n';
    report += '='.repeat(title.length) + '\n\n';

    // Timestamp
    if (includeTimestamp) {
      report += `Generated: ${new Date().toLocaleString()}\n\n`;
    }

    // Statistics
    if (includeStatistics && data.statistics) {
      report += 'STATISTICAL SUMMARY\n';
      report += '-------------------\n';
      
      Object.entries(data.statistics).forEach(([key, value]) => {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        const formattedValue = typeof value === 'number' ? value.toFixed(4) : value;
        report += `${formattedKey}: ${formattedValue}\n`;
      });
      
      report += '\n';
    }

    // Raw data
    if (includeRawData && data.raw) {
      report += 'RAW DATA\n';
      report += '--------\n';
      report += data.raw.join(', ') + '\n\n';
    }

    // Groups
    if (data.groups && data.groups.length > 0) {
      report += 'GROUPS\n';
      report += '------\n';
      report += data.groups.join(', ') + '\n\n';
    }

    // Metadata
    if (data.metadata) {
      report += 'METADATA\n';
      report += '--------\n';
      Object.entries(data.metadata).forEach(([key, value]) => {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        report += `${formattedKey}: ${value}\n`;
      });
    }

    return report;
  }

  /**
   * Check if browser supports file downloads
   * @returns {boolean} Whether downloads are supported
   */
  supportsDownload() {
    return typeof document !== 'undefined' && 
           typeof document.createElement === 'function' &&
           typeof URL !== 'undefined' &&
           typeof URL.createObjectURL === 'function';
  }

  /**
   * Check if browser supports file reading
   * @returns {boolean} Whether file reading is supported
   */
  supportsFileReading() {
    return typeof FileReader !== 'undefined';
  }

  /**
   * Get file info
   * @param {File} file - File to get info for
   * @returns {Object} File information
   */
  getFileInfo(file) {
    if (!file) return null;

    return {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      extension: this.getFileExtension(file.name),
      formattedSize: this.formatFileSize(file.size),
      lastModifiedDate: new Date(file.lastModified)
    };
  }
}
