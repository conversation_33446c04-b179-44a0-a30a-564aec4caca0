/**
 * Test Chart.js Registration
 * This script tests if all required Chart.js components are properly registered
 */

import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Filler,
  Bar<PERSON><PERSON>roller,
  LineC<PERSON>roller,
  ScatterController
} from 'chart.js';

// Register Chart.js components
Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  BarController,
  LineController,
  ScatterController
);

console.log('🧪 Testing Chart.js registration...');

// Test 1: Check if Chart is available
console.log('Chart available:', typeof Chart !== 'undefined');

// Test 2: Check registry
console.log('Chart registry:', Chart.registry);

// Test 3: Test controller registration
const controllers = ['bar', 'line', 'scatter'];
controllers.forEach(type => {
  try {
    const controller = Chart.registry.getController(type);
    console.log(`✅ ${type} controller:`, controller ? 'registered' : 'not found');
  } catch (error) {
    console.error(`❌ ${type} controller error:`, error.message);
  }
});

// Test 4: Test scale registration
const scales = ['category', 'linear'];
scales.forEach(type => {
  try {
    const scale = Chart.registry.getScale(type);
    console.log(`✅ ${type} scale:`, scale ? 'registered' : 'not found');
  } catch (error) {
    console.error(`❌ ${type} scale error:`, error.message);
  }
});

// Test 5: Test element registration
const elements = ['bar', 'line', 'point'];
elements.forEach(type => {
  try {
    const element = Chart.registry.getElement(type);
    console.log(`✅ ${type} element:`, element ? 'registered' : 'not found');
  } catch (error) {
    console.error(`❌ ${type} element error:`, error.message);
  }
});

// Test 6: Try creating a simple chart
export function testChartCreation(canvasId) {
  console.log('🧪 Testing chart creation...');
  
  const canvas = document.getElementById(canvasId);
  if (!canvas) {
    console.error('❌ Canvas not found:', canvasId);
    return false;
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.error('❌ Cannot get 2D context');
    return false;
  }

  try {
    // Destroy any existing chart
    const existingChart = Chart.getChart(canvas);
    if (existingChart) {
      console.log('🗑️ Destroying existing chart');
      existingChart.destroy();
    }

    const chart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Test 1', 'Test 2', 'Test 3'],
        datasets: [{
          label: 'Test Data',
          data: [10, 20, 30],
          backgroundColor: '#3498db'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    });

    console.log('✅ Test chart created successfully:', chart.id);
    return chart;
  } catch (error) {
    console.error('❌ Chart creation failed:', error);
    return false;
  }
}

console.log('✅ Chart.js registration test completed');
