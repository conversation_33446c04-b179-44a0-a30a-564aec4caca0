/**
 * Main Application Entry Point
 */

// Import and register Chart.js components first
import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Register Chart.js components globally
Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

import { DataProcessor } from './data/dataProcessor.js';
import { ChartManager } from './charts/chartManager.js';
import { VariabilityPlot } from './charts/variabilityPlot.js';
import { UIManager } from './components/uiManager.js';
import { NotificationManager } from './utils/notifications.js';
import { FileManager } from './utils/fileManager.js';
import { SampleDataGenerator } from './utils/sampleData.js';

class MinitabWebApp {
  constructor() {
    console.log('🚀 Initializing MinitabWebApp...');

    try {
      this.dataProcessor = new DataProcessor();
      console.log('✅ DataProcessor created');

      this.chartManager = new ChartManager();
      console.log('✅ ChartManager created');

      this.variabilityPlot = new VariabilityPlot(this.chartManager);
      console.log('✅ VariabilityPlot created');

      this.uiManager = new UIManager();
      console.log('✅ UIManager created');

      this.notifications = new NotificationManager();
      console.log('✅ NotificationManager created');

      this.fileManager = new FileManager();
      console.log('✅ FileManager created');

      this.sampleData = new SampleDataGenerator();
      console.log('✅ SampleDataGenerator created');

      this.currentData = null;
      this.currentChart = null;

      this.init();
    } catch (error) {
      console.error('❌ Error initializing MinitabWebApp:', error);
    }
  }

  /**
   * Initialize the application
   */
  init() {
    console.log('🔧 Setting up event listeners...');
    this.setupEventListeners();

    console.log('🎨 Initializing UI manager...');
    this.uiManager.init();

    console.log('📢 Showing welcome notification...');
    this.showNotification('Application loaded successfully!', 'success');

    console.log('✅ MinitabWebApp initialization complete!');
  }

  /**
   * Setup all event listeners
   */
  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // Data input events
    document.getElementById('load-manual-data').addEventListener('click', () => {
      this.loadManualData();
    });

    document.getElementById('csv-file').addEventListener('change', (e) => {
      this.loadCSVFile(e.target.files[0]);
    });

    // Sample data buttons
    document.querySelectorAll('[data-sample]').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.loadSampleData(e.target.dataset.sample);
      });
    });

    // Plot generation
    document.getElementById('generate-plot').addEventListener('click', () => {
      this.generatePlot();
    });

    // Chart actions
    document.getElementById('export-chart').addEventListener('click', () => {
      this.exportCurrentChart();
    });

    document.getElementById('fullscreen-chart').addEventListener('click', () => {
      this.toggleFullscreen();
    });

    // Export buttons
    document.getElementById('export-csv').addEventListener('click', () => {
      this.exportData('csv');
    });

    document.getElementById('export-json').addEventListener('click', () => {
      this.exportData('json');
    });

    document.getElementById('export-txt').addEventListener('click', () => {
      this.exportData('txt');
    });

    document.getElementById('export-png').addEventListener('click', () => {
      this.exportChart('png');
    });

    document.getElementById('export-jpeg').addEventListener('click', () => {
      this.exportChart('jpeg');
    });

    document.getElementById('export-report').addEventListener('click', () => {
      this.exportReport();
    });

    // Real-time updates for chart options
    document.getElementById('chart-title').addEventListener('input', (e) => {
      document.getElementById('chart-title-display').textContent = e.target.value || 'Chart';
    });
  }

  /**
   * Switch between tabs
   * @param {string} tabName - Name of the tab to switch to
   */
  switchTab(tabName) {
    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
  }

  /**
   * Load manual data input
   */
  async loadManualData() {
    try {
      this.showLoading(true);
      
      const dataInput = document.getElementById('manual-data').value.trim();
      const groupInput = document.getElementById('group-data').value.trim();
      
      if (!dataInput) {
        throw new Error('Please enter some data');
      }

      // Parse data
      const data = this.dataProcessor.parseStringData(dataInput);
      let groups = [];
      
      if (groupInput) {
        groups = groupInput.split(/[,;\s\t]+/).filter(g => g.trim() !== '');
        if (groups.length !== data.length) {
          throw new Error(`Number of groups (${groups.length}) must match number of data points (${data.length})`);
        }
      }

      // Set data in processor
      this.dataProcessor.setData(data, groups);
      this.currentData = this.dataProcessor.getPlotData();
      
      this.updateDataSummary();
      this.updateStatistics();
      this.showNotification(`Loaded ${data.length} data points successfully!`, 'success');
      
      // Switch to analysis tab
      this.switchTab('analysis');
      
    } catch (error) {
      this.showNotification(error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Load CSV file
   * @param {File} file - CSV file to load
   */
  async loadCSVFile(file) {
    if (!file) return;
    
    try {
      this.showLoading(true);
      
      const csvText = await this.fileManager.readFileAsText(file);
      const csvData = this.dataProcessor.parseCSVData(csvText);
      
      // Show CSV preview
      this.showCSVPreview(csvData);
      
      // For now, assume first column is data, second is groups (if exists)
      const data = csvData.rows.map(row => parseFloat(row[0])).filter(val => !isNaN(val));
      const groups = csvData.headers.length > 1 ? csvData.rows.map(row => row[1]) : [];
      
      if (data.length === 0) {
        throw new Error('No valid numerical data found in CSV');
      }

      this.dataProcessor.setData(data, groups);
      this.currentData = this.dataProcessor.getPlotData();
      
      this.updateDataSummary();
      this.updateStatistics();
      this.showNotification(`Loaded ${data.length} data points from CSV!`, 'success');
      
    } catch (error) {
      this.showNotification(error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Load sample data
   * @param {string} sampleType - Type of sample data to load
   */
  loadSampleData(sampleType) {
    console.log(`📊 Loading sample data: ${sampleType}`);

    try {
      let data, groups;

      switch (sampleType) {
        case 'normal':
          console.log('📈 Generating normal distribution...');
          data = this.sampleData.generateNormalDistribution(50, 100, 15);
          break;
        case 'skewed':
          console.log('📈 Generating skewed data...');
          data = this.sampleData.generateSkewedData(40);
          break;
        case 'grouped':
          console.log('📈 Generating grouped data...');
          const groupedData = this.sampleData.generateGroupedData();
          data = groupedData.data;
          groups = groupedData.groups;
          break;
        case 'outliers':
          console.log('📈 Generating data with outliers...');
          data = this.sampleData.generateDataWithOutliers(30);
          break;
        default:
          throw new Error('Unknown sample data type');
      }

      console.log(`✅ Generated ${data.length} data points:`, data.slice(0, 5), '...');

      this.dataProcessor.setData(data, groups || []);
      this.currentData = this.dataProcessor.getPlotData();

      console.log('📊 Plot data prepared:', this.currentData);

      this.updateDataSummary();
      this.updateStatistics();
      this.showNotification(`Loaded ${sampleType} sample data (${data.length} points)!`, 'success');

      // Switch to analysis tab
      this.switchTab('analysis');

    } catch (error) {
      console.error('❌ Error loading sample data:', error);
      this.showNotification(error.message, 'error');
    }
  }

  /**
   * Generate plot based on current settings
   */
  async generatePlot() {
    console.log('🎨 Generate plot requested...');

    if (!this.currentData) {
      console.log('❌ No data available');
      this.showNotification('Please load data first', 'warning');
      return;
    }

    try {
      console.log('⏳ Starting plot generation...');
      this.showLoading(true);

      const plotType = document.getElementById('plot-type').value;
      const confidence = parseFloat(document.getElementById('confidence-level').value);
      const title = document.getElementById('chart-title').value || 'Chart';

      console.log(`📊 Plot settings: type=${plotType}, confidence=${confidence}, title="${title}"`);

      const options = {
        title,
        confidence,
        showMean: document.getElementById('show-mean').checked,
        showConfidenceInterval: document.getElementById('show-confidence').checked,
        showGrid: document.getElementById('show-grid').checked,
        showLegend: document.getElementById('show-legend').checked
      };

      console.log('⚙️ Plot options:', options);

      // Destroy existing chart
      if (this.currentChart) {
        console.log('🗑️ Destroying existing chart...');
        this.chartManager.destroyChart('main-chart');
      }

      // Hide placeholder, show canvas
      const placeholder = document.getElementById('chart-placeholder');
      const canvas = document.getElementById('main-chart');

      console.log('🎨 Canvas element:', canvas);
      console.log('📋 Placeholder element:', placeholder);

      if (placeholder) placeholder.classList.add('hidden');
      if (canvas) canvas.style.display = 'block';

      // Generate appropriate chart
      console.log(`🎯 Creating ${plotType} chart...`);

      switch (plotType) {
        case 'histogram':
          console.log('📊 Creating histogram with data:', this.currentData.histogram);
          this.currentChart = this.chartManager.createHistogram('main-chart', this.currentData.histogram, options);
          break;
        case 'boxplot':
          console.log('📦 Creating boxplot with data:', this.currentData.boxplot);
          this.currentChart = this.chartManager.createBoxPlot('main-chart', this.currentData.boxplot, options);
          break;
        case 'variability':
          console.log('📈 Creating variability plot...');
          this.currentChart = this.chartManager.createVariabilityPlot('main-chart', this.currentData, options);
          break;
        case 'individual':
          console.log('📉 Creating individual plot...');
          this.currentChart = this.variabilityPlot.createIndividualPlot('main-chart', this.currentData, options);
          break;
        case 'movingRange':
          console.log('📊 Creating moving range plot...');
          this.currentChart = this.variabilityPlot.createMovingRangePlot('main-chart', this.currentData, options);
          break;
        case 'scatter':
          console.log('🔵 Creating scatter plot...');
          this.currentChart = this.chartManager.createScatterPlot('main-chart', this.currentData.groupedStats, options);
          break;
        default:
          throw new Error('Unknown plot type');
      }

      console.log('✅ Chart created successfully:', this.currentChart);
      this.showNotification(`${plotType} chart generated successfully!`, 'success');

    } catch (error) {
      console.error('❌ Error generating plot:', error);
      this.showNotification(error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Update data summary display
   */
  updateDataSummary() {
    if (!this.currentData) return;
    
    const stats = this.currentData.statistics;
    const summary = document.getElementById('data-summary');
    
    document.getElementById('summary-count').textContent = stats.count;
    document.getElementById('summary-mean').textContent = stats.mean.toFixed(3);
    document.getElementById('summary-median').textContent = stats.median.toFixed(3);
    document.getElementById('summary-std').textContent = stats.standardDeviation.toFixed(3);
    document.getElementById('summary-min').textContent = stats.min.toFixed(3);
    document.getElementById('summary-max').textContent = stats.max.toFixed(3);
    
    summary.classList.remove('hidden');
  }

  /**
   * Update statistics panel
   */
  updateStatistics() {
    if (!this.currentData) return;
    
    const stats = this.currentData.statistics;
    const content = document.getElementById('statistics-content');
    
    content.innerHTML = `
      <div class="summary-grid">
        <div class="summary-item">
          <span class="summary-label">Count:</span>
          <span class="summary-value">${stats.count}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Mean:</span>
          <span class="summary-value">${stats.mean.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Median:</span>
          <span class="summary-value">${stats.median.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Std Dev:</span>
          <span class="summary-value">${stats.standardDeviation.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Variance:</span>
          <span class="summary-value">${stats.variance.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Min:</span>
          <span class="summary-value">${stats.min.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Max:</span>
          <span class="summary-value">${stats.max.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Range:</span>
          <span class="summary-value">${stats.range.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Q1:</span>
          <span class="summary-value">${stats.q1.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Q3:</span>
          <span class="summary-value">${stats.q3.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">IQR:</span>
          <span class="summary-value">${stats.iqr.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Skewness:</span>
          <span class="summary-value">${stats.skewness.toFixed(4)}</span>
        </div>
      </div>
    `;
  }

  /**
   * Show CSV preview
   * @param {Object} csvData - Parsed CSV data
   */
  showCSVPreview(csvData) {
    const preview = document.getElementById('csv-preview');
    const maxRows = 5;
    
    let html = '<h4>CSV Preview:</h4><table class="csv-table">';
    html += '<thead><tr>';
    csvData.headers.forEach(header => {
      html += `<th>${header}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    csvData.rows.slice(0, maxRows).forEach(row => {
      html += '<tr>';
      row.forEach(cell => {
        html += `<td>${cell}</td>`;
      });
      html += '</tr>';
    });
    
    if (csvData.rows.length > maxRows) {
      html += `<tr><td colspan="${csvData.headers.length}">... and ${csvData.rows.length - maxRows} more rows</td></tr>`;
    }
    
    html += '</tbody></table>';
    preview.innerHTML = html;
    preview.classList.remove('hidden');
  }

  /**
   * Export data in specified format
   * @param {string} format - Export format
   */
  exportData(format) {
    if (!this.currentData) {
      this.showNotification('No data to export', 'warning');
      return;
    }

    try {
      const exportedData = this.dataProcessor.exportData(format);
      this.fileManager.downloadFile(exportedData, `data.${format}`, this.getContentType(format));
      this.showNotification(`Data exported as ${format.toUpperCase()}!`, 'success');
    } catch (error) {
      this.showNotification(error.message, 'error');
    }
  }

  /**
   * Export current chart
   * @param {string} format - Image format
   */
  exportChart(format) {
    if (!this.currentChart) {
      this.showNotification('No chart to export', 'warning');
      return;
    }

    try {
      const imageData = this.chartManager.exportChart('main-chart', format);
      this.fileManager.downloadImageFromDataURL(imageData, `chart.${format}`);
      this.showNotification(`Chart exported as ${format.toUpperCase()}!`, 'success');
    } catch (error) {
      this.showNotification(error.message, 'error');
    }
  }

  /**
   * Export current chart from button
   */
  exportCurrentChart() {
    this.exportChart('png');
  }

  /**
   * Toggle fullscreen for chart
   */
  toggleFullscreen() {
    const chartCard = document.querySelector('.chart-card');
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      chartCard.requestFullscreen();
    }
  }

  /**
   * Export comprehensive report
   */
  exportReport() {
    if (!this.currentData) {
      this.showNotification('No data to include in report', 'warning');
      return;
    }

    try {
      const reportTitle = document.getElementById('report-title').value || 'Statistical Analysis Report';
      const includeData = document.getElementById('include-data').checked;
      const includeStats = document.getElementById('include-statistics').checked;
      const includeChart = document.getElementById('include-chart').checked;

      let report = `${reportTitle}\n${'='.repeat(reportTitle.length)}\n\n`;
      report += `Generated on: ${new Date().toLocaleString()}\n\n`;

      if (includeStats) {
        report += 'STATISTICAL SUMMARY\n';
        report += '-------------------\n';
        const stats = this.currentData.statistics;
        Object.entries(stats).forEach(([key, value]) => {
          const formattedValue = typeof value === 'number' ? value.toFixed(4) : value;
          report += `${key}: ${formattedValue}\n`;
        });
        report += '\n';
      }

      if (includeData) {
        report += 'RAW DATA\n';
        report += '--------\n';
        report += this.currentData.raw.join(', ') + '\n\n';
        
        if (this.currentData.groups.length > 0) {
          report += 'GROUPS\n';
          report += '------\n';
          report += this.currentData.groups.join(', ') + '\n\n';
        }
      }

      this.fileManager.downloadFile(report, `${reportTitle.replace(/\s+/g, '_')}.txt`, 'text/plain');
      this.showNotification('Report exported successfully!', 'success');
    } catch (error) {
      this.showNotification(error.message, 'error');
    }
  }

  /**
   * Show loading overlay
   * @param {boolean} show - Whether to show or hide loading
   */
  showLoading(show) {
    const overlay = document.getElementById('loading-overlay');
    overlay.classList.toggle('hidden', !show);
  }

  /**
   * Show notification
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, warning, info)
   */
  showNotification(message, type = 'info') {
    this.notifications.show(message, type);
  }

  /**
   * Get content type for file format
   * @param {string} format - File format
   * @returns {string} Content type
   */
  getContentType(format) {
    const types = {
      'csv': 'text/csv',
      'json': 'application/json',
      'txt': 'text/plain'
    };
    return types[format] || 'text/plain';
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new MinitabWebApp();
});
