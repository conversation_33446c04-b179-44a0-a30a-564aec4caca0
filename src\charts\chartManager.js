/**
 * Chart Manager - Handles all chart creation and management using Chart.js
 */

import { Chart } from 'chart.js';

export class ChartManager {
  constructor() {
    this.charts = new Map();
    this.defaultColors = [
      '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
      '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
    ];
  }

  /**
   * Create a histogram chart
   * @param {string} canvasId - Canvas element ID
   * @param {Object} histogramData - Histogram data from DataProcessor
   * @param {Object} options - Chart options
   * @returns {Chart} Chart.js instance
   */
  createHistogram(canvasId, histogramData, options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas element with ID '${canvasId}' not found`);
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error(`Failed to get 2D context for canvas '${canvasId}'`);
    }

    // Destroy existing chart if it exists
    this.destroyChart(canvasId);

    const config = {
      type: 'bar',
      data: {
        labels: histogramData.labels,
        datasets: [{
          label: 'Frequency',
          data: histogramData.bins,
          backgroundColor: options.backgroundColor || this.defaultColors[0] + '80',
          borderColor: options.borderColor || this.defaultColors[0],
          borderWidth: 1,
          barPercentage: 0.95,
          categoryPercentage: 0.95
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: options.title || 'Histogram',
            font: { size: 16, weight: 'bold' }
          },
          legend: {
            display: options.showLegend !== false
          },
          tooltip: {
            callbacks: {
              title: (context) => `Range: ${context[0].label}`,
              label: (context) => `Frequency: ${context.parsed.y}`
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: options.xAxisLabel || 'Value Range'
            },
            grid: {
              display: options.showGrid !== false
            }
          },
          y: {
            title: {
              display: true,
              text: options.yAxisLabel || 'Frequency'
            },
            beginAtZero: true,
            grid: {
              display: options.showGrid !== false
            }
          }
        },
        ...options.chartOptions
      }
    };

    const chart = new Chart(ctx, config);
    this.charts.set(canvasId, chart);
    return chart;
  }

  /**
   * Create a box plot chart (using custom drawing)
   * @param {string} canvasId - Canvas element ID
   * @param {Object} boxplotData - Box plot data from DataProcessor
   * @param {Object} options - Chart options
   * @returns {Chart} Chart.js instance
   */
  createBoxPlot(canvasId, boxplotData, options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas element with ID '${canvasId}' not found`);
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error(`Failed to get 2D context for canvas '${canvasId}'`);
    }

    this.destroyChart(canvasId);

    // Create a scatter plot and draw box plot manually
    const config = {
      type: 'scatter',
      data: {
        datasets: [
          {
            label: 'Outliers',
            data: boxplotData.outliers.map((value, index) => ({ x: 1, y: value })),
            backgroundColor: options.outlierColor || '#e74c3c',
            borderColor: options.outlierColor || '#e74c3c',
            pointRadius: 4,
            pointHoverRadius: 6
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: options.title || 'Box Plot',
            font: { size: 16, weight: 'bold' }
          },
          legend: {
            display: options.showLegend !== false
          },
          tooltip: {
            callbacks: {
              title: () => 'Box Plot',
              label: (context) => {
                if (context.datasetIndex === 0) {
                  return `Outlier: ${context.parsed.y.toFixed(2)}`;
                }
                return '';
              }
            }
          }
        },
        scales: {
          x: {
            type: 'linear',
            min: 0.5,
            max: 1.5,
            display: false
          },
          y: {
            title: {
              display: true,
              text: options.yAxisLabel || 'Value'
            },
            min: Math.min(boxplotData.min, ...boxplotData.outliers) * 0.95,
            max: Math.max(boxplotData.max, ...boxplotData.outliers) * 1.05
          }
        },
        onHover: (event, elements) => {
          event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
        }
      },
      plugins: [{
        id: 'boxPlotDrawer',
        afterDatasetsDraw: (chart) => {
          this.drawBoxPlot(chart, boxplotData, options);
        }
      }]
    };

    const chart = new Chart(ctx, config);
    this.charts.set(canvasId, chart);
    return chart;
  }

  /**
   * Create a variability plot (line chart with confidence intervals)
   * @param {string} canvasId - Canvas element ID
   * @param {Object} data - Plot data
   * @param {Object} options - Chart options
   * @returns {Chart} Chart.js instance
   */
  createVariabilityPlot(canvasId, data, options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas element with ID '${canvasId}' not found`);
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error(`Failed to get 2D context for canvas '${canvasId}'`);
    }

    this.destroyChart(canvasId);

    const datasets = [];

    // Main data line
    datasets.push({
      label: options.dataLabel || 'Data Points',
      data: data.raw.map((value, index) => ({ x: index + 1, y: value })),
      borderColor: options.lineColor || this.defaultColors[0],
      backgroundColor: options.lineColor || this.defaultColors[0],
      fill: false,
      tension: 0.1,
      pointRadius: 3,
      pointHoverRadius: 5
    });

    // Mean line
    if (options.showMean !== false) {
      const meanLine = Array(data.raw.length).fill(data.statistics.mean);
      datasets.push({
        label: 'Mean',
        data: meanLine.map((value, index) => ({ x: index + 1, y: value })),
        borderColor: options.meanColor || '#e74c3c',
        backgroundColor: options.meanColor || '#e74c3c',
        borderDash: [5, 5],
        fill: false,
        pointRadius: 0
      });
    }

    // Confidence interval
    if (options.showConfidenceInterval && data.confidenceInterval) {
      const upperBound = Array(data.raw.length).fill(data.confidenceInterval.upper);
      const lowerBound = Array(data.raw.length).fill(data.confidenceInterval.lower);
      
      datasets.push({
        label: `${(options.confidence || 95)}% Confidence Interval`,
        data: upperBound.map((value, index) => ({ x: index + 1, y: value })),
        borderColor: options.ciColor || '#95a5a6',
        backgroundColor: options.ciColor || '#95a5a6' + '40',
        borderDash: [3, 3],
        fill: '+1',
        pointRadius: 0
      });

      datasets.push({
        label: '',
        data: lowerBound.map((value, index) => ({ x: index + 1, y: value })),
        borderColor: options.ciColor || '#95a5a6',
        backgroundColor: options.ciColor || '#95a5a6' + '40',
        borderDash: [3, 3],
        fill: false,
        pointRadius: 0
      });
    }

    const config = {
      type: 'line',
      data: { datasets },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: options.title || 'Variability Plot',
            font: { size: 16, weight: 'bold' }
          },
          legend: {
            display: options.showLegend !== false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              title: (context) => `Data Point ${context[0].label}`,
              label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(3)}`
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: options.xAxisLabel || 'Observation'
            },
            grid: {
              display: options.showGrid !== false
            }
          },
          y: {
            title: {
              display: true,
              text: options.yAxisLabel || 'Value'
            },
            grid: {
              display: options.showGrid !== false
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    };

    const chart = new Chart(ctx, config);
    this.charts.set(canvasId, chart);
    return chart;
  }

  /**
   * Create a scatter plot for grouped data
   * @param {string} canvasId - Canvas element ID
   * @param {Object} groupedData - Grouped data from DataProcessor
   * @param {Object} options - Chart options
   * @returns {Chart} Chart.js instance
   */
  createScatterPlot(canvasId, groupedData, options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
      throw new Error(`Canvas element with ID '${canvasId}' not found`);
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error(`Failed to get 2D context for canvas '${canvasId}'`);
    }

    this.destroyChart(canvasId);

    const datasets = Object.keys(groupedData).map((groupName, index) => {
      const groupStats = groupedData[groupName];
      const color = this.defaultColors[index % this.defaultColors.length];

      return {
        label: groupName,
        data: groupStats.raw ? groupStats.raw.map((value, idx) => ({ x: idx + 1, y: value })) : [],
        backgroundColor: color + '80',
        borderColor: color,
        pointRadius: 4,
        pointHoverRadius: 6
      };
    });

    const config = {
      type: 'scatter',
      data: { datasets },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: options.title || 'Scatter Plot by Group',
            font: { size: 16, weight: 'bold' }
          },
          legend: {
            display: options.showLegend !== false
          },
          tooltip: {
            callbacks: {
              title: (context) => `${context[0].dataset.label} - Point ${context[0].label}`,
              label: (context) => `Value: ${context.parsed.y.toFixed(3)}`
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: options.xAxisLabel || 'Observation'
            }
          },
          y: {
            title: {
              display: true,
              text: options.yAxisLabel || 'Value'
            }
          }
        }
      }
    };

    const chart = new Chart(ctx, config);
    this.charts.set(canvasId, chart);
    return chart;
  }

  /**
   * Draw box plot elements on canvas
   * @param {Chart} chart - Chart.js instance
   * @param {Object} boxplotData - Box plot data
   * @param {Object} options - Drawing options
   */
  drawBoxPlot(chart, boxplotData, options = {}) {
    const ctx = chart.ctx;
    const chartArea = chart.chartArea;
    const yScale = chart.scales.y;

    const boxWidth = 60;
    const centerX = chartArea.left + (chartArea.right - chartArea.left) / 2;
    const leftX = centerX - boxWidth / 2;
    const rightX = centerX + boxWidth / 2;

    // Convert data values to pixel coordinates
    const q1Y = yScale.getPixelForValue(boxplotData.q1);
    const medianY = yScale.getPixelForValue(boxplotData.median);
    const q3Y = yScale.getPixelForValue(boxplotData.q3);
    const whiskerLowY = yScale.getPixelForValue(boxplotData.whiskerLow);
    const whiskerHighY = yScale.getPixelForValue(boxplotData.whiskerHigh);

    ctx.save();
    ctx.strokeStyle = options.boxColor || '#2c3e50';
    ctx.fillStyle = options.boxFillColor || '#ecf0f1';
    ctx.lineWidth = 2;

    // Draw box (IQR)
    ctx.fillRect(leftX, q3Y, boxWidth, q1Y - q3Y);
    ctx.strokeRect(leftX, q3Y, boxWidth, q1Y - q3Y);

    // Draw median line
    ctx.beginPath();
    ctx.moveTo(leftX, medianY);
    ctx.lineTo(rightX, medianY);
    ctx.lineWidth = 3;
    ctx.strokeStyle = options.medianColor || '#e74c3c';
    ctx.stroke();

    // Draw whiskers
    ctx.strokeStyle = options.whiskerColor || '#2c3e50';
    ctx.lineWidth = 2;

    // Upper whisker
    ctx.beginPath();
    ctx.moveTo(centerX, q3Y);
    ctx.lineTo(centerX, whiskerHighY);
    ctx.moveTo(centerX - 15, whiskerHighY);
    ctx.lineTo(centerX + 15, whiskerHighY);
    ctx.stroke();

    // Lower whisker
    ctx.beginPath();
    ctx.moveTo(centerX, q1Y);
    ctx.lineTo(centerX, whiskerLowY);
    ctx.moveTo(centerX - 15, whiskerLowY);
    ctx.lineTo(centerX + 15, whiskerLowY);
    ctx.stroke();

    ctx.restore();
  }

  /**
   * Destroy a specific chart
   * @param {string} canvasId - Canvas element ID
   */
  destroyChart(canvasId) {
    const chart = this.charts.get(canvasId);
    if (chart) {
      chart.destroy();
      this.charts.delete(canvasId);
    }
  }

  /**
   * Destroy all charts
   */
  destroyAllCharts() {
    this.charts.forEach(chart => chart.destroy());
    this.charts.clear();
  }

  /**
   * Get chart instance
   * @param {string} canvasId - Canvas element ID
   * @returns {Chart|null} Chart.js instance or null
   */
  getChart(canvasId) {
    return this.charts.get(canvasId) || null;
  }

  /**
   * Export chart as image
   * @param {string} canvasId - Canvas element ID
   * @param {string} format - Image format ('png', 'jpeg')
   * @returns {string} Data URL of the image
   */
  exportChart(canvasId, format = 'png') {
    const chart = this.charts.get(canvasId);
    if (!chart) {
      throw new Error(`Chart with ID '${canvasId}' not found`);
    }

    return chart.toBase64Image(`image/${format}`, 1.0);
  }

  /**
   * Get default chart options for different chart types
   * @param {string} chartType - Type of chart
   * @returns {Object} Default options
   */
  getDefaultOptions(chartType) {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        }
      }
    };

    switch (chartType) {
      case 'histogram':
        return {
          ...baseOptions,
          scales: {
            y: {
              beginAtZero: true,
              title: { display: true, text: 'Frequency' }
            },
            x: {
              title: { display: true, text: 'Value Range' }
            }
          }
        };

      case 'boxplot':
        return {
          ...baseOptions,
          scales: {
            y: {
              title: { display: true, text: 'Value' }
            }
          }
        };

      case 'variability':
        return {
          ...baseOptions,
          scales: {
            x: {
              title: { display: true, text: 'Observation' }
            },
            y: {
              title: { display: true, text: 'Value' }
            }
          },
          interaction: {
            mode: 'index',
            intersect: false
          }
        };

      default:
        return baseOptions;
    }
  }
}
