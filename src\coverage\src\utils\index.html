
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.59% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>854/1322</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.92% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>60/77</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">49.2% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>31/63</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.59% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>854/1322</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="fileManager.js"><a href="fileManager.js.html">fileManager.js</a></td>
	<td data-value="48.63" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 48%"></div><div class="cover-empty" style="width: 52%"></div></div>
	</td>
	<td data-value="48.63" class="pct low">48.63%</td>
	<td data-value="440" class="abs low">214/440</td>
	<td data-value="69.56" class="pct medium">69.56%</td>
	<td data-value="23" class="abs medium">16/23</td>
	<td data-value="30" class="pct low">30%</td>
	<td data-value="20" class="abs low">6/20</td>
	<td data-value="48.63" class="pct low">48.63%</td>
	<td data-value="440" class="abs low">214/440</td>
	</tr>

<tr>
	<td class="file medium" data-value="notifications.js"><a href="notifications.js.html">notifications.js</a></td>
	<td data-value="66.73" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.73" class="pct medium">66.73%</td>
	<td data-value="460" class="abs medium">307/460</td>
	<td data-value="72" class="pct medium">72%</td>
	<td data-value="25" class="abs medium">18/25</td>
	<td data-value="48" class="pct low">48%</td>
	<td data-value="25" class="abs low">12/25</td>
	<td data-value="66.73" class="pct medium">66.73%</td>
	<td data-value="460" class="abs medium">307/460</td>
	</tr>

<tr>
	<td class="file medium" data-value="sampleData.js"><a href="sampleData.js.html">sampleData.js</a></td>
	<td data-value="78.9" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.9" class="pct medium">78.9%</td>
	<td data-value="422" class="abs medium">333/422</td>
	<td data-value="89.65" class="pct high">89.65%</td>
	<td data-value="29" class="abs high">26/29</td>
	<td data-value="72.22" class="pct medium">72.22%</td>
	<td data-value="18" class="abs medium">13/18</td>
	<td data-value="78.9" class="pct medium">78.9%</td>
	<td data-value="422" class="abs medium">333/422</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-23T17:32:41.910Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    