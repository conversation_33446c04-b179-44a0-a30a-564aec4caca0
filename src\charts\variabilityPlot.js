/**
 * Specialized Variability Plot implementation similar to Minitab
 */

import { Chart } from 'chart.js';
import { ChartManager } from './chartManager.js';
import { calculateConfidenceInterval } from '../data/statistics.js';

export class VariabilityPlot {
  constructor(chartManager = null) {
    this.chartManager = chartManager || new ChartManager();
    this.plotTypes = {
      INDIVIDUAL: 'individual',
      RANGE: 'range',
      MOVING_RANGE: 'movingRange',
      CONTROL: 'control'
    };
  }

  /**
   * Create an individual values plot (I-Chart)
   * @param {string} canvasId - Canvas element ID
   * @param {Object} plotData - Data from DataProcessor
   * @param {Object} options - Plot options
   * @returns {Chart} Chart.js instance
   */
  createIndividualPlot(canvasId, plotData, options = {}) {
    console.log('📉 Creating I-Chart with data:', plotData);

    const data = plotData.raw;
    const stats = plotData.statistics;
    
    // Calculate control limits (±3σ from mean)
    const upperControlLimit = stats.mean + 3 * stats.standardDeviation;
    const lowerControlLimit = stats.mean - 3 * stats.standardDeviation;

    console.log('📉 Control limits - UCL:', upperControlLimit, 'LCL:', lowerControlLimit, 'Mean:', stats.mean);
    
    // Calculate confidence interval
    const confidenceInterval = calculateConfidenceInterval(data, options.confidence || 0.95);

    const datasets = [
      {
        label: 'Individual Values',
        data: data.map((value, index) => ({ x: index + 1, y: value })),
        borderColor: '#3498db',
        backgroundColor: '#3498db',
        fill: false,
        tension: 0,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointBackgroundColor: data.map(value => {
          if (value > upperControlLimit || value < lowerControlLimit) {
            return '#e74c3c'; // Red for out-of-control points
          }
          return '#3498db';
        })
      },
      {
        label: 'Center Line (Mean)',
        data: Array(data.length).fill(stats.mean).map((value, index) => ({ x: index + 1, y: value })),
        borderColor: '#2ecc71',
        backgroundColor: '#2ecc71',
        borderDash: [5, 5],
        fill: false,
        pointRadius: 0,
        tension: 0
      },
      {
        label: 'Upper Control Limit (UCL)',
        data: Array(data.length).fill(upperControlLimit).map((value, index) => ({ x: index + 1, y: value })),
        borderColor: '#e74c3c',
        backgroundColor: '#e74c3c',
        borderDash: [10, 5],
        fill: false,
        pointRadius: 0,
        tension: 0
      },
      {
        label: 'Lower Control Limit (LCL)',
        data: Array(data.length).fill(lowerControlLimit).map((value, index) => ({ x: index + 1, y: value })),
        borderColor: '#e74c3c',
        backgroundColor: '#e74c3c',
        borderDash: [10, 5],
        fill: false,
        pointRadius: 0,
        tension: 0
      }
    ];

    // Add confidence interval if requested
    if (options.showConfidenceInterval) {
      datasets.push({
        label: `${(options.confidence || 95)}% Confidence Interval (Upper)`,
        data: Array(data.length).fill(confidenceInterval.upper).map((value, index) => ({ x: index + 1, y: value })),
        borderColor: '#95a5a6',
        backgroundColor: '#95a5a6' + '40',
        borderDash: [3, 3],
        fill: '+1',
        pointRadius: 0,
        tension: 0
      });

      datasets.push({
        label: `${(options.confidence || 95)}% Confidence Interval (Lower)`,
        data: Array(data.length).fill(confidenceInterval.lower).map((value, index) => ({ x: index + 1, y: value })),
        borderColor: '#95a5a6',
        backgroundColor: '#95a5a6' + '40',
        borderDash: [3, 3],
        fill: false,
        pointRadius: 0,
        tension: 0
      });
    }

    const chartOptions = {
      title: options.title || 'Individual Values Plot (I-Chart)',
      xAxisLabel: options.xAxisLabel || 'Observation',
      yAxisLabel: options.yAxisLabel || 'Individual Value',
      showLegend: options.showLegend !== false,
      showGrid: options.showGrid !== false,
      chartOptions: {
        plugins: {
          tooltip: {
            callbacks: {
              title: (context) => `Observation ${context[0].label}`,
              label: (context) => {
                const value = context.parsed.y;
                let label = `${context.dataset.label}: ${value.toFixed(3)}`;
                
                if (context.datasetIndex === 0) {
                  // Add control limit status for individual values
                  if (value > upperControlLimit) {
                    label += ' (Above UCL)';
                  } else if (value < lowerControlLimit) {
                    label += ' (Below LCL)';
                  } else {
                    label += ' (In Control)';
                  }
                }
                
                return label;
              }
            }
          },
          annotation: {
            annotations: this.createControlLimitAnnotations(upperControlLimit, lowerControlLimit, stats.mean)
          }
        },
        scales: {
          y: {
            min: Math.min(lowerControlLimit * 1.1, Math.min(...data) * 0.9),
            max: Math.max(upperControlLimit * 1.1, Math.max(...data) * 1.1)
          }
        }
      }
    };

    // Create the chart using line chart type for I-Chart
    const config = {
      type: 'line',
      data: { datasets },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: chartOptions.title,
            font: { size: 16, weight: 'bold' }
          },
          legend: {
            display: chartOptions.showLegend
          },
          tooltip: chartOptions.chartOptions.plugins.tooltip
        },
        scales: {
          x: {
            title: {
              display: true,
              text: chartOptions.xAxisLabel
            },
            grid: {
              display: chartOptions.showGrid
            }
          },
          y: {
            title: {
              display: true,
              text: chartOptions.yAxisLabel
            },
            grid: {
              display: chartOptions.showGrid
            },
            ...chartOptions.chartOptions.scales.y
          }
        }
      }
    };

    const canvas = document.getElementById(canvasId);
    const ctx = canvas.getContext('2d');
    this.chartManager.destroyChart(canvasId);

    try {
      const chart = new Chart(ctx, config);
      this.chartManager.charts.set(canvasId, chart);
      return chart;
    } catch (error) {
      console.error('❌ Failed to create I-Chart:', error);
      throw new Error(`Failed to create I-Chart: ${error.message}`);
    }
  }

  /**
   * Create a moving range plot (MR-Chart)
   * @param {string} canvasId - Canvas element ID
   * @param {Object} plotData - Data from DataProcessor
   * @param {Object} options - Plot options
   * @returns {Chart} Chart.js instance
   */
  createMovingRangePlot(canvasId, plotData, options = {}) {
    console.log('📊 Creating MR-Chart with data:', plotData);

    const data = plotData.raw;
    
    // Calculate moving ranges
    const movingRanges = [];
    for (let i = 1; i < data.length; i++) {
      movingRanges.push(Math.abs(data[i] - data[i - 1]));
    }

    if (movingRanges.length === 0) {
      throw new Error('Need at least 2 data points to calculate moving ranges');
    }

    // Calculate average moving range
    const avgMovingRange = movingRanges.reduce((sum, range) => sum + range, 0) / movingRanges.length;
    
    // Control limits for moving range chart (using D3 and D4 constants for n=2)
    const D3 = 0; // Lower control limit factor for n=2
    const D4 = 3.267; // Upper control limit factor for n=2
    
    const upperControlLimit = D4 * avgMovingRange;
    const lowerControlLimit = D3 * avgMovingRange; // Always 0 for n=2

    const datasets = [
      {
        label: 'Moving Range',
        data: movingRanges.map((range, index) => ({ x: index + 2, y: range })),
        borderColor: '#9b59b6',
        backgroundColor: '#9b59b6',
        fill: false,
        tension: 0,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointBackgroundColor: movingRanges.map(range => {
          if (range > upperControlLimit) {
            return '#e74c3c'; // Red for out-of-control points
          }
          return '#9b59b6';
        })
      },
      {
        label: 'Center Line (Average MR)',
        data: Array(movingRanges.length).fill(avgMovingRange).map((value, index) => ({ x: index + 2, y: value })),
        borderColor: '#2ecc71',
        backgroundColor: '#2ecc71',
        borderDash: [5, 5],
        fill: false,
        pointRadius: 0,
        tension: 0
      },
      {
        label: 'Upper Control Limit (UCL)',
        data: Array(movingRanges.length).fill(upperControlLimit).map((value, index) => ({ x: index + 2, y: value })),
        borderColor: '#e74c3c',
        backgroundColor: '#e74c3c',
        borderDash: [10, 5],
        fill: false,
        pointRadius: 0,
        tension: 0
      }
    ];

    // Only add LCL if it's greater than 0
    if (lowerControlLimit > 0) {
      datasets.push({
        label: 'Lower Control Limit (LCL)',
        data: Array(movingRanges.length).fill(lowerControlLimit).map((value, index) => ({ x: index + 2, y: value })),
        borderColor: '#e74c3c',
        backgroundColor: '#e74c3c',
        borderDash: [10, 5],
        fill: false,
        pointRadius: 0,
        tension: 0
      });
    }

    const chartOptions = {
      title: options.title || 'Moving Range Plot (MR-Chart)',
      xAxisLabel: options.xAxisLabel || 'Observation',
      yAxisLabel: options.yAxisLabel || 'Moving Range',
      showLegend: options.showLegend !== false,
      showGrid: options.showGrid !== false,
      chartOptions: {
        plugins: {
          tooltip: {
            callbacks: {
              title: (context) => `Observations ${context[0].label - 1}-${context[0].label}`,
              label: (context) => {
                const value = context.parsed.y;
                let label = `${context.dataset.label}: ${value.toFixed(3)}`;
                
                if (context.datasetIndex === 0) {
                  if (value > upperControlLimit) {
                    label += ' (Above UCL)';
                  } else {
                    label += ' (In Control)';
                  }
                }
                
                return label;
              }
            }
          }
        },
        scales: {
          x: {
            min: 1.5,
            max: data.length + 0.5
          },
          y: {
            min: 0,
            max: Math.max(upperControlLimit * 1.1, Math.max(...movingRanges) * 1.1)
          }
        }
      }
    };

    // Create the chart using line chart type for MR-Chart
    const config = {
      type: 'line',
      data: { datasets },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: chartOptions.title,
            font: { size: 16, weight: 'bold' }
          },
          legend: {
            display: chartOptions.showLegend
          },
          tooltip: chartOptions.chartOptions.plugins.tooltip
        },
        scales: {
          x: {
            title: {
              display: true,
              text: chartOptions.xAxisLabel
            },
            grid: {
              display: chartOptions.showGrid
            },
            ...chartOptions.chartOptions.scales.x
          },
          y: {
            title: {
              display: true,
              text: chartOptions.yAxisLabel
            },
            grid: {
              display: chartOptions.showGrid
            },
            ...chartOptions.chartOptions.scales.y
          }
        }
      }
    };

    const canvas = document.getElementById(canvasId);
    const ctx = canvas.getContext('2d');
    this.chartManager.destroyChart(canvasId);

    try {
      const chart = new Chart(ctx, config);
      this.chartManager.charts.set(canvasId, chart);
      return chart;
    } catch (error) {
      console.error('❌ Failed to create MR-Chart:', error);
      throw new Error(`Failed to create MR-Chart: ${error.message}`);
    }
  }

  /**
   * Create control limit annotations
   * @param {number} ucl - Upper control limit
   * @param {number} lcl - Lower control limit
   * @param {number} centerLine - Center line value
   * @returns {Object} Annotation configuration
   */
  createControlLimitAnnotations(ucl, lcl, centerLine) {
    return {
      ucl: {
        type: 'line',
        yMin: ucl,
        yMax: ucl,
        borderColor: '#e74c3c',
        borderWidth: 2,
        borderDash: [10, 5],
        label: {
          content: 'UCL',
          enabled: true,
          position: 'end'
        }
      },
      lcl: {
        type: 'line',
        yMin: lcl,
        yMax: lcl,
        borderColor: '#e74c3c',
        borderWidth: 2,
        borderDash: [10, 5],
        label: {
          content: 'LCL',
          enabled: true,
          position: 'end'
        }
      },
      centerLine: {
        type: 'line',
        yMin: centerLine,
        yMax: centerLine,
        borderColor: '#2ecc71',
        borderWidth: 2,
        borderDash: [5, 5],
        label: {
          content: 'CL',
          enabled: true,
          position: 'end'
        }
      }
    };
  }

  /**
   * Analyze control chart patterns
   * @param {number[]} data - Data points
   * @param {number} ucl - Upper control limit
   * @param {number} lcl - Lower control limit
   * @param {number} centerLine - Center line value
   * @returns {Object} Analysis results
   */
  analyzeControlPatterns(data, ucl, lcl, centerLine) {
    const patterns = {
      outOfControl: [],
      trends: [],
      runs: [],
      cycles: []
    };

    // Rule 1: Points beyond control limits
    data.forEach((value, index) => {
      if (value > ucl || value < lcl) {
        patterns.outOfControl.push({
          index: index + 1,
          value,
          rule: 'Beyond control limits'
        });
      }
    });

    // Rule 2: 7 consecutive points on same side of center line
    let consecutiveAbove = 0;
    let consecutiveBelow = 0;
    
    data.forEach((value, index) => {
      if (value > centerLine) {
        consecutiveAbove++;
        consecutiveBelow = 0;
        if (consecutiveAbove >= 7) {
          patterns.runs.push({
            startIndex: index - 6,
            endIndex: index + 1,
            rule: '7 consecutive points above center line'
          });
        }
      } else if (value < centerLine) {
        consecutiveBelow++;
        consecutiveAbove = 0;
        if (consecutiveBelow >= 7) {
          patterns.runs.push({
            startIndex: index - 6,
            endIndex: index + 1,
            rule: '7 consecutive points below center line'
          });
        }
      } else {
        consecutiveAbove = 0;
        consecutiveBelow = 0;
      }
    });

    // Rule 3: 6 consecutive increasing or decreasing points
    for (let i = 0; i <= data.length - 6; i++) {
      let increasing = true;
      let decreasing = true;
      
      for (let j = i; j < i + 5; j++) {
        if (data[j] >= data[j + 1]) increasing = false;
        if (data[j] <= data[j + 1]) decreasing = false;
      }
      
      if (increasing) {
        patterns.trends.push({
          startIndex: i + 1,
          endIndex: i + 6,
          rule: '6 consecutive increasing points'
        });
      }
      
      if (decreasing) {
        patterns.trends.push({
          startIndex: i + 1,
          endIndex: i + 6,
          rule: '6 consecutive decreasing points'
        });
      }
    }

    return patterns;
  }
}
