// Debug script to test module loading and functionality
console.log('🔍 Debug script starting...');

// Test DOM elements
document.addEventListener('DOMContentLoaded', () => {
  console.log('📄 DOM loaded');

  // Check if canvas exists
  const canvas = document.getElementById('main-chart');
  console.log('🎨 Canvas element:', canvas);

  // Check if buttons exist
  const generateBtn = document.getElementById('generate-plot');
  console.log('🔘 Generate button:', generateBtn);

  const sampleBtns = document.querySelectorAll('[data-sample]');
  console.log('📊 Sample buttons:', sampleBtns.length);
});

// Test Chart.js import
import('chart.js').then(chartModule => {
  console.log('✅ Chart.js loaded successfully:', Object.keys(chartModule));

  // Test creating a simple chart
  setTimeout(() => {
    const canvas = document.getElementById('main-chart');
    if (canvas) {
      try {
        const ctx = canvas.getContext('2d');
        const chart = new chartModule.Chart(ctx, {
          type: 'bar',
          data: {
            labels: ['A', 'B', 'C'],
            datasets: [{
              label: 'Test Data',
              data: [1, 2, 3],
              backgroundColor: '#3498db'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false
          }
        });
        console.log('✅ Test chart created successfully:', chart);
      } catch (error) {
        console.error('❌ Failed to create test chart:', error);
      }
    } else {
      console.error('❌ Canvas not found for test chart');
    }
  }, 1000);

}).catch(error => {
  console.error('❌ Failed to load Chart.js:', error);
});

// Test our modules
import('./data/dataProcessor.js').then(module => {
  console.log('✅ DataProcessor loaded successfully');
  try {
    const processor = new module.DataProcessor();
    console.log('✅ DataProcessor instance created');

    // Test with sample data
    const testData = [1, 2, 3, 4, 5];
    processor.setData(testData);
    const stats = processor.getStatistics();
    console.log('✅ Statistics calculated:', stats);
  } catch (error) {
    console.error('❌ DataProcessor error:', error);
  }
}).catch(error => {
  console.error('❌ Failed to load DataProcessor:', error);
});

import('./utils/sampleData.js').then(module => {
  console.log('✅ SampleDataGenerator loaded successfully');
  try {
    const generator = new module.SampleDataGenerator();
    const sampleData = generator.generateNormalDistribution(10, 50, 10);
    console.log('✅ Sample data generated:', sampleData);
  } catch (error) {
    console.error('❌ SampleDataGenerator error:', error);
  }
}).catch(error => {
  console.error('❌ Failed to load SampleDataGenerator:', error);
});

console.log('🔍 Debug script setup completed');
