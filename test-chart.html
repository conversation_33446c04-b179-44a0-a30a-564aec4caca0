<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>Chart.js Test</h1>
    <div style="width: 400px; height: 300px;">
        <canvas id="test-chart"></canvas>
    </div>

    <script>
        console.log('Testing Chart.js...');
        
        // Test if Chart.js is available
        console.log('Chart.js available:', typeof Chart !== 'undefined');
        
        // Test creating a simple bar chart
        try {
            const ctx = document.getElementById('test-chart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['A', 'B', 'C'],
                    datasets: [{
                        label: 'Test Data',
                        data: [1, 2, 3],
                        backgroundColor: '#3498db'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            console.log('✅ Chart created successfully:', chart);
        } catch (error) {
            console.error('❌ Failed to create chart:', error);
        }
    </script>
</body>
</html>
