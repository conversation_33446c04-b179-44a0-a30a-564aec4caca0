var Rn=Object.defineProperty;var Bn=(i,t,e)=>t in i?Rn(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var S=(i,t,e)=>Bn(i,typeof t!="symbol"?t+"":t,e);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&s(r)}).observe(document,{childList:!0,subtree:!0});function e(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(n){if(n.ep)return;n.ep=!0;const o=e(n);fetch(n.href,o)}})();function Di(i){if(!Array.isArray(i)||i.length===0)throw new Error("Data must be a non-empty array");const t=i.filter(n=>typeof n=="number"&&!isNaN(n));if(t.length===0)throw new Error("No valid numerical data found");const e=[...t].sort((n,o)=>n-o);return{count:t.length,mean:ee(t),median:zn(e),mode:Nn(t),standardDeviation:Re(t),variance:Ws(t),min:Math.min(...t),max:Math.max(...t),range:Math.max(...t)-Math.min(...t),q1:re(e,.25),q3:re(e,.75),iqr:re(e,.75)-re(e,.25),skewness:Hn(t),kurtosis:Vn(t)}}function ee(i){return i.reduce((t,e)=>t+e,0)/i.length}function zn(i){const t=i.length,e=Math.floor(t/2);return t%2===0?(i[e-1]+i[e])/2:i[e]}function Nn(i){const t={};let e=0;return i.forEach(s=>{t[s]=(t[s]||0)+1,e=Math.max(e,t[s])}),Object.keys(t).filter(s=>t[s]===e).map(Number)}function Re(i,t=!0){return Math.sqrt(Ws(i,t))}function Ws(i,t=!0){const e=ee(i),s=i.map(o=>Math.pow(o-e,2)),n=t?i.length-1:i.length;return s.reduce((o,r)=>o+r,0)/n}function re(i,t){const e=t*(i.length-1),s=Math.floor(e),n=Math.ceil(e),o=e%1;return n>=i.length?i[i.length-1]:s<0?i[0]:i[s]*(1-o)+i[n]*o}function Hn(i){const t=ee(i),e=Re(i),s=i.length,n=i.reduce((o,r)=>o+Math.pow((r-t)/e,3),0);return s/((s-1)*(s-2))*n}function Vn(i){const t=ee(i),e=Re(i),s=i.length,n=i.reduce((o,r)=>o+Math.pow((r-t)/e,4),0);return s*(s+1)/((s-1)*(s-2)*(s-3))*n-3*Math.pow(s-1,2)/((s-2)*(s-3))}function $n(i,t=.95){const e=ee(i),s=Re(i)/Math.sqrt(i.length),n=1-t,r=Wn(i.length-1,n/2)*s;return{lower:e-r,upper:e+r,margin:r}}function Wn(i,t){return t<=.025?2.228:t<=.05?1.833:t<=.1?1.372:1.282}class jn{constructor(){this.data=[],this.groups=[],this.metadata={}}parseStringData(t){if(typeof t!="string")throw new Error("Input must be a string");const e=t.trim();if(!e)throw new Error("Input cannot be empty");const s=e.split(/[,;\s\t]+/).filter(n=>n.trim()!=="").map(n=>{const o=parseFloat(n.trim());if(isNaN(o))throw new Error(`Invalid number: "${n}"`);return o});if(s.length===0)throw new Error("No valid numbers found in input");return s}parseCSVData(t){if(!t||t.trim()==="")throw new Error("CSV data is empty");const e=t.trim().split(`
`);if(e.length===0)throw new Error("CSV data is empty");const s=e[0].split(",").map(o=>o.trim()),n=[];for(let o=1;o<e.length;o++){const r=e[o].split(",").map(a=>a.trim());if(r.length!==s.length)throw new Error(`Row ${o} has ${r.length} values but expected ${s.length}`);n.push(r)}return{headers:s,rows:n}}validateAndCleanData(t){if(!Array.isArray(t))throw new Error("Data must be an array");const e=[];for(const s of t)if(!(s==null||s===""))if(typeof s=="string"){const n=parseFloat(s);isNaN(n)||e.push(n)}else typeof s=="number"&&!isNaN(s)&&e.push(s);if(e.length===0)throw new Error("No valid data after cleaning");return e}setData(t,e=[]){this.data=this.validateAndCleanData(t),this.groups=e,this.metadata={originalLength:t.length,cleanedLength:this.data.length,removedCount:t.length-this.data.length,hasGroups:e.length>0}}getStatistics(){if(this.data.length===0)throw new Error("No data available for analysis");return Di(this.data)}getGroupedStatistics(){if(this.groups.length===0)return{"All Data":this.getStatistics()};const t={};return[...new Set(this.groups)].forEach(s=>{const n=this.data.filter((o,r)=>this.groups[r]===s);n.length>0&&(t[s]=Di(n))}),t}getPlotData(){const t=this.getStatistics();return{raw:this.data,groups:this.groups,statistics:t,groupedStats:this.getGroupedStatistics(),metadata:this.metadata,histogram:this.generateHistogramData(),boxplot:this.generateBoxplotData()}}generateHistogramData(t=null){if(this.data.length===0)return null;const e=Math.min(...this.data),s=Math.max(...this.data);t||(t=Math.ceil(Math.log2(this.data.length)+1),t=Math.max(5,Math.min(t,20)));const n=(s-e)/t,o=Array(t).fill(0),r=[];for(let a=0;a<t;a++){const l=e+a*n,c=e+(a+1)*n;r.push(`${l.toFixed(2)}-${c.toFixed(2)}`)}return this.data.forEach(a=>{let l=Math.floor((a-e)/n);l>=t&&(l=t-1),o[l]++}),{bins:o,labels:r,binWidth:n,min:e,max:s}}generateBoxplotData(){if(this.data.length===0)return null;[...this.data].sort((r,a)=>r-a);const t=this.getStatistics(),e=t.q1-1.5*t.iqr,s=t.q3+1.5*t.iqr,n=this.data.filter(r=>r<e||r>s),o=this.data.filter(r=>r>=e&&r<=s);return{min:t.min,q1:t.q1,median:t.median,q3:t.q3,max:t.max,outliers:n,whiskerLow:o.length>0?Math.min(...o):t.min,whiskerHigh:o.length>0?Math.max(...o):t.max,iqr:t.iqr}}exportData(t="csv"){switch(t.toLowerCase()){case"csv":return this.exportCSV();case"json":return this.exportJSON();case"txt":return this.exportTXT();default:throw new Error(`Unsupported export format: ${t}`)}}exportCSV(){const t=this.getStatistics();let e=`Data
`;return e+=this.data.join(`
`),e+=`

Statistics
`,e+=Object.entries(t).map(([s,n])=>`${s},${n}`).join(`
`),e}exportJSON(){return JSON.stringify({data:this.data,groups:this.groups,statistics:this.getStatistics(),metadata:this.metadata},null,2)}exportTXT(){const t=this.getStatistics();let e=`Dataset Summary
`;return e+=`===============

`,e+=`Data: ${this.data.join(", ")}

`,e+=`Statistics:
`,Object.entries(t).forEach(([s,n])=>{e+=`${s}: ${typeof n=="number"?n.toFixed(4):n}
`}),e}}/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function ie(i){return i+.5|0}const ht=(i,t,e)=>Math.max(Math.min(i,e),t);function Wt(i){return ht(ie(i*2.55),0,255)}function gt(i){return ht(ie(i*255),0,255)}function ct(i){return ht(ie(i/2.55)/100,0,1)}function Li(i){return ht(ie(i*100),0,100)}const Y={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ii=[..."0123456789ABCDEF"],Un=i=>ii[i&15],qn=i=>ii[(i&240)>>4]+ii[i&15],ae=i=>(i&240)>>4===(i&15),Yn=i=>ae(i.r)&&ae(i.g)&&ae(i.b)&&ae(i.a);function Xn(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&Y[i[1]]*17,g:255&Y[i[2]]*17,b:255&Y[i[3]]*17,a:t===5?Y[i[4]]*17:255}:(t===7||t===9)&&(e={r:Y[i[1]]<<4|Y[i[2]],g:Y[i[3]]<<4|Y[i[4]],b:Y[i[5]]<<4|Y[i[6]],a:t===9?Y[i[7]]<<4|Y[i[8]]:255})),e}const Gn=(i,t)=>i<255?t(i):"";function Kn(i){var t=Yn(i)?Un:qn;return i?"#"+t(i.r)+t(i.g)+t(i.b)+Gn(i.a,t):void 0}const Qn=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function js(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function Zn(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Jn(i,t,e){const s=js(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function to(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function gi(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=to(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function pi(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(gt)}function mi(i,t,e){return pi(js,i,t,e)}function eo(i,t,e){return pi(Jn,i,t,e)}function io(i,t,e){return pi(Zn,i,t,e)}function Us(i){return(i%360+360)%360}function so(i){const t=Qn.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?Wt(+t[5]):gt(+t[5]));const n=Us(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=eo(n,o,r):t[1]==="hsv"?s=io(n,o,r):s=mi(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function no(i,t){var e=gi(i);e[0]=Us(e[0]+t),e=mi(e),i.r=e[0],i.g=e[1],i.b=e[2]}function oo(i){if(!i)return;const t=gi(i),e=t[0],s=Li(t[1]),n=Li(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${ct(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const Pi={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Ti={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function ro(){const i={},t=Object.keys(Ti),e=Object.keys(Pi);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,Pi[o]);o=parseInt(Ti[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let le;function ao(i){le||(le=ro(),le.transparent=[0,0,0,0]);const t=le[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const lo=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function co(i){const t=lo.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?Wt(r):ht(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?Wt(s):ht(s,0,255)),n=255&(t[4]?Wt(n):ht(n,0,255)),o=255&(t[6]?Wt(o):ht(o,0,255)),{r:s,g:n,b:o,a:e}}}function ho(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${ct(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const We=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Pt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function uo(i,t,e){const s=Pt(ct(i.r)),n=Pt(ct(i.g)),o=Pt(ct(i.b));return{r:gt(We(s+e*(Pt(ct(t.r))-s))),g:gt(We(n+e*(Pt(ct(t.g))-n))),b:gt(We(o+e*(Pt(ct(t.b))-o))),a:i.a+e*(t.a-i.a)}}function ce(i,t,e){if(i){let s=gi(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=mi(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function qs(i,t){return i&&Object.assign(t||{},i)}function Oi(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=gt(i[3]))):(t=qs(i,{r:0,g:0,b:0,a:1}),t.a=gt(t.a)),t}function fo(i){return i.charAt(0)==="r"?co(i):so(i)}class Kt{constructor(t){if(t instanceof Kt)return t;const e=typeof t;let s;e==="object"?s=Oi(t):e==="string"&&(s=Xn(t)||ao(t)||fo(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=qs(this._rgb);return t&&(t.a=ct(t.a)),t}set rgb(t){this._rgb=Oi(t)}rgbString(){return this._valid?ho(this._rgb):void 0}hexString(){return this._valid?Kn(this._rgb):void 0}hslString(){return this._valid?oo(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=uo(this._rgb,t._rgb,e)),this}clone(){return new Kt(this.rgb)}alpha(t){return this._rgb.a=gt(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=ie(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ce(this._rgb,2,t),this}darken(t){return ce(this._rgb,2,-t),this}saturate(t){return ce(this._rgb,1,t),this}desaturate(t){return ce(this._rgb,1,-t),this}rotate(t){return no(this._rgb,t),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function rt(){}const go=(()=>{let i=0;return()=>i++})();function I(i){return i==null}function B(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function L(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function $(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function et(i,t){return $(i)?i:t}function D(i,t){return typeof i>"u"?t:i}const po=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function E(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function O(i,t,e,s){let n,o,r;if(B(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(L(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function Ce(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function De(i){if(B(i))return i.map(De);if(L(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=De(i[e[n]]);return t}return i}function Ys(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function mo(i,t,e,s){if(!Ys(i))return;const n=t[i],o=e[i];L(n)&&L(o)?Qt(n,o,s):t[i]=De(o)}function Qt(i,t,e){const s=B(t)?t:[t],n=s.length;if(!L(i))return i;e=e||{};const o=e.merger||mo;let r;for(let a=0;a<n;++a){if(r=s[a],!L(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function qt(i,t){return Qt(i,t,{merger:bo})}function bo(i,t,e){if(!Ys(i))return;const s=t[i],n=e[i];L(s)&&L(n)?qt(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=De(n))}const Ai={"":i=>i,x:i=>i.x,y:i=>i.y};function yo(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function xo(i){const t=yo(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function Le(i,t){return(Ai[t]||(Ai[t]=xo(t)))(i)}function bi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Pe=i=>typeof i<"u",pt=i=>typeof i=="function",Ei=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function _o(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const z=Math.PI,J=2*z,vo=J+z,Te=Number.POSITIVE_INFINITY,wo=z/180,Q=z/2,yt=z/4,Ii=z*2/3,Xs=Math.log10,At=Math.sign;function Yt(i,t,e){return Math.abs(i-t)<e}function Fi(i){const t=Math.round(i);i=Yt(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(Xs(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Mo(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function ko(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function Oe(i){return!ko(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function So(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Co(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function Mt(i){return i*(z/180)}function Do(i){return i*(180/z)}function Ri(i){if(!$(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function Lo(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*z&&(o+=J),{angle:o,distance:n}}function si(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function Po(i,t){return(i-t+vo)%J-z}function nt(i){return(i%J+J)%J}function Gs(i,t,e,s){const n=nt(i),o=nt(t),r=nt(e),a=nt(o-n),l=nt(r-n),c=nt(n-o),h=nt(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function Z(i,t,e){return Math.max(t,Math.min(e,i))}function To(i){return Z(i,-32768,32767)}function dt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function yi(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const ni=(i,t,e,s)=>yi(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),Oo=(i,t,e)=>yi(i,e,s=>i[s][t]>=e);function Ao(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Ks=["push","pop","shift","splice","unshift"];function Eo(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Ks.forEach(e=>{const s="_onData"+bi(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function Bi(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Ks.forEach(o=>{delete i[o]}),delete i._chartjs)}function Io(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Qs=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Zs(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Qs.call(window,()=>{s=!1,i.apply(t,e)}))}}function Fo(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const xi=i=>i==="start"?"left":i==="end"?"right":"center",H=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,Ro=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t,he=i=>i===0||i===1,zi=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*J/e)),Ni=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*J/e)+1,Xt={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*Q)+1,easeOutSine:i=>Math.sin(i*Q),easeInOutSine:i=>-.5*(Math.cos(z*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>he(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>he(i)?i:zi(i,.075,.3),easeOutElastic:i=>he(i)?i:Ni(i,.075,.3),easeInOutElastic(i){return he(i)?i:i<.5?.5*zi(i*2,.1125,.45):.5+.5*Ni(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Xt.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Xt.easeInBounce(i*2)*.5:Xt.easeOutBounce(i*2-1)*.5+.5};function _i(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Hi(i){return _i(i)?i:new Kt(i)}function je(i){return _i(i)?i:new Kt(i).saturate(.5).darken(.1).hexString()}const Bo=["x","y","borderWidth","radius","tension"],zo=["color","borderColor","backgroundColor"];function No(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:zo},numbers:{type:"number",properties:Bo}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Ho(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Vi=new Map;function Vo(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Vi.get(e);return s||(s=new Intl.NumberFormat(i,t),Vi.set(e,s)),s}function Js(i,t,e){return Vo(t,e).format(i)}const $o={values(i){return B(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Wo(i,e)}const r=Xs(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Js(i,s,l)}};function Wo(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var tn={formatters:$o};function jo(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:tn.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const St=Object.create(null),oi=Object.create(null);function Gt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Ue(i,t,e){return typeof t=="string"?Qt(Gt(i,t),e):Qt(Gt(i,""),t)}class Uo{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>je(n.backgroundColor),this.hoverBorderColor=(s,n)=>je(n.borderColor),this.hoverColor=(s,n)=>je(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ue(this,t,e)}get(t){return Gt(this,t)}describe(t,e){return Ue(oi,t,e)}override(t,e){return Ue(St,t,e)}route(t,e,s,n){const o=Gt(this,t),r=Gt(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return L(l)?Object.assign({},c,l):D(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var R=new Uo({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[No,Ho,jo]);function qo(i){return!i||I(i.size)||I(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function $i(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function xt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Wi(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function ri(i,t,e,s){en(i,t,e,s,null)}function en(i,t,e,s,n){let o,r,a,l,c,h,d,u;const f=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*wo;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,p,0,0,J):i.arc(e,s,p,0,J),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=Ii,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=Ii,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+yt)*l,d=Math.cos(m+yt)*(n?n/2-c:l),a=Math.sin(m+yt)*l,u=Math.sin(m+yt)*(n?n/2-c:l),i.arc(e-d,s-a,c,m-z,m-Q),i.arc(e+u,s-r,c,m-Q,m),i.arc(e+d,s+a,c,m,m+Q),i.arc(e-u,s+r,c,m+Q,m+z),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=yt;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+u,s-r),i.lineTo(e+d,s+a),i.lineTo(e-u,s+r),i.closePath();break;case"crossRot":m+=yt;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r),m+=yt,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function Zt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Be(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function ze(i){i.restore()}function Yo(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function Xo(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function Go(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),I(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Ko(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function Qo(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Jt(i,t,e,s,n,o={}){const r=B(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,Go(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Qo(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),I(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Ko(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function Ae(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*z,z,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,z,Q,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,Q,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-Q,!0),i.lineTo(e+r.topLeft,s)}const Zo=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Jo=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function tr(i,t){const e=(""+i).match(Zo);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const er=i=>+i||0;function sn(i,t){const e={},s=L(t),n=s?Object.keys(t):t,o=L(i)?s?r=>D(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=er(o(r));return e}function nn(i){return sn(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Tt(i){return sn(i,["topLeft","topRight","bottomLeft","bottomRight"])}function K(i){const t=nn(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function V(i,t){i=i||{},t=t||R.font;let e=D(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=D(i.style,t.style);s&&!(""+s).match(Jo)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:D(i.family,t.family),lineHeight:tr(D(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:D(i.weight,t.weight),string:""};return n.string=qo(n),n}function de(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&r!==void 0)return r}function ir(i,t,e){const{min:s,max:n}=i,o=po(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function Ct(i,t){return Object.assign(Object.create(i),t)}function vi(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=ln("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>vi([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return rn(a,l,()=>hr(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return Ui(a).includes(l)},ownKeys(a){return Ui(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function Et(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:on(i,s),setContext:o=>Et(i,o,e,s),override:o=>Et(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return rn(o,r,()=>nr(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function on(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:pt(e)?e:()=>e,isIndexable:pt(s)?s:()=>s}}const sr=(i,t)=>i?i+bi(t):t,wi=(i,t)=>L(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function rn(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function nr(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return pt(a)&&r.isScriptable(t)&&(a=or(t,a,i,e)),B(a)&&a.length&&(a=rr(t,a,i,r.isIndexable)),wi(t,a)&&(a=Et(a,n,o&&o[t],r)),a}function or(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),wi(i,l)&&(l=Mi(n._scopes,n,i,l)),l}function rr(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(L(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=Mi(c,n,i,h);t.push(Et(d,o,r&&r[i],a))}}return t}function an(i,t,e){return pt(i)?i(t,e):i}const ar=(i,t)=>i===!0?t:typeof i=="string"?Le(t,i):void 0;function lr(i,t,e,s,n){for(const o of t){const r=ar(e,o);if(r){i.add(r);const a=an(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function Mi(i,t,e,s){const n=t._rootScopes,o=an(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=ji(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=ji(a,r,o,l,s),l===null)?!1:vi(Array.from(a),[""],n,o,()=>cr(t,e,s))}function ji(i,t,e,s,n){for(;e;)e=lr(i,t,e,s,n);return e}function cr(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return B(n)&&L(e)?e:n||{}}function hr(i,t,e,s){let n;for(const o of t)if(n=ln(sr(o,i),e),typeof n<"u")return wi(i,n)?Mi(e,s,i,n):n}function ln(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Ui(i){let t=i._keys;return t||(t=i._keys=dr(i._scopes)),t}function dr(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}const ur=Number.EPSILON||1e-14,It=(i,t)=>t<i.length&&!i[t].skip&&i[t],cn=i=>i==="x"?"y":"x";function fr(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=si(o,n),l=si(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,u=s*h;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+u*(r.x-n.x),y:o.y+u*(r.y-n.y)}}}function gr(i,t,e){const s=i.length;let n,o,r,a,l,c=It(i,0);for(let h=0;h<s-1;++h)if(l=c,c=It(i,h+1),!(!l||!c)){if(Yt(t[h],0,ur)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function pr(i,t,e="x"){const s=cn(e),n=i.length;let o,r,a,l=It(i,0);for(let c=0;c<n;++c){if(r=a,a=l,l=It(i,c+1),!a)continue;const h=a[e],d=a[s];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${s}`]=d+o*t[c])}}function mr(i,t="x"){const e=cn(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,c=It(i,0);for(r=0;r<s;++r)if(a=l,l=c,c=It(i,r+1),!!l){if(c){const h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?At(n[r-1])!==At(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}gr(i,n,o),pr(i,o,t)}function ue(i,t,e){return Math.max(Math.min(i,e),t)}function br(i,t){let e,s,n,o,r,a=Zt(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&Zt(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=ue(n.cp1x,t.left,t.right),n.cp1y=ue(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=ue(n.cp2x,t.left,t.right),n.cp2y=ue(n.cp2y,t.top,t.bottom)))}function yr(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")mr(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=fr(c,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&br(i,e)}function ki(){return typeof window<"u"&&typeof document<"u"}function Si(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Ee(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Ne=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function xr(i,t){return Ne(i).getPropertyValue(t)}const _r=["top","right","bottom","left"];function kt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=_r[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const vr=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function wr(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(vr(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function vt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Ne(e),o=n.boxSizing==="border-box",r=kt(n,"padding"),a=kt(n,"border","width"),{x:l,y:c,box:h}=wr(i,e),d=r.left+(h&&a.left),u=r.top+(h&&a.top);let{width:f,height:g}=t;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-d)/f*e.width/s),y:Math.round((c-u)/g*e.height/s)}}function Mr(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&Si(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=Ne(o),l=kt(a,"border","width"),c=kt(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=Ee(a.maxWidth,o,"clientWidth"),n=Ee(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||Te,maxHeight:n||Te}}const fe=i=>Math.round(i*10)/10;function kr(i,t,e,s){const n=Ne(i),o=kt(n,"margin"),r=Ee(n.maxWidth,i,"clientWidth")||Te,a=Ee(n.maxHeight,i,"clientHeight")||Te,l=Mr(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const u=kt(n,"border","width"),f=kt(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=fe(Math.min(c,r,l.maxWidth)),h=fe(Math.min(h,a,l.maxHeight)),c&&!h&&(h=fe(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=fe(Math.floor(h*s))),{width:c,height:h}}function qi(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Sr=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};ki()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Yi(i,t){const e=xr(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function wt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function Cr(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function Dr(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=wt(i,n,e),a=wt(n,o,e),l=wt(o,t,e),c=wt(r,a,e),h=wt(a,l,e);return wt(c,h,e)}const Lr=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},Pr=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Ot(i,t,e){return i?Lr(t,e):Pr()}function hn(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function dn(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function un(i){return i==="angle"?{between:Gs,compare:Po,normalize:nt}:{between:dt,compare:(t,e)=>t-e,normalize:t=>t}}function Xi({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function Tr(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=un(s),l=t.length;let{start:c,end:h,loop:d}=i,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&r(a(t[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function fn(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=un(s),{start:h,end:d,loop:u,style:f}=Tr(i,t,e),g=[];let p=!1,m=null,b,y,_;const v=()=>l(n,_,b)&&a(n,_)!==0,x=()=>a(o,b)===0||l(o,_,b),M=()=>p||v(),k=()=>!p||x();for(let w=h,C=h;w<=d;++w)y=t[w%r],!y.skip&&(b=c(y[s]),b!==_&&(p=l(b,n,o),m===null&&M()&&(m=a(b,n)===0?w:C),m!==null&&k()&&(g.push(Xi({start:m,end:w,loop:u,count:r,style:f})),m=null),C=w,_=b));return m!==null&&g.push(Xi({start:m,end:d,loop:u,count:r,style:f})),g}function gn(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=fn(s[n],i.points,t);o.length&&e.push(...o)}return e}function Or(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function Ar(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function Er(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=Or(e,n,o,s);if(s===!0)return Gi(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,c=!!i._fullLoop&&r===0&&a===n-1;return Gi(i,Ar(e,r,l,c),e,t)}function Gi(i,t,e,s){return!s||!s.setContext||!e?t:Ir(i,t,e,s)}function Ir(i,t,e,s){const n=i._chart.getContext(),o=Ki(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,c=[];let h=o,d=t[0].start,u=d;function f(g,p,m,b){const y=a?-1:1;if(g!==p){for(g+=l;e[g%l].skip;)g-=y;for(;e[p%l].skip;)p+=y;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(const g of t){d=a?d:g.start;let p=e[d%l],m;for(u=d+1;u<=g.end;u++){const b=e[u%l];m=Ki(s.setContext(Ct(n,{type:"segment",p0:p,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),Fr(m,h)&&f(d,u-1,g.loop,h),p=b,h=m}d<u-1&&f(d,u-1,g.loop,h)}return c}function Ki(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function Fr(i,t){if(!t)return!1;const e=[],s=function(n,o){return _i(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}function ge(i,t,e){return i.options.clip?i[e]:t[e]}function Rr(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:ge(e,t,"left"),right:ge(e,t,"right"),top:ge(s,t,"top"),bottom:ge(s,t,"bottom")}:t}function pn(i,t){const e=t._clip;if(e.disabled)return!1;const s=Rr(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Br{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Qs.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var at=new Br;const Qi="transparent",zr={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Hi(i||Qi),n=s.valid&&Hi(t||Qi);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class Nr{constructor(t,e,s,n){const o=e[s];n=de([t.to,n,o,t.from]);const r=de([t.from,o,n]);this._active=!0,this._fn=t.fn||zr[t.type||typeof r],this._easing=Xt[t.easing]||Xt.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=de([t.to,e,n,t.from]),this._from=de([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class mn{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!L(t))return;const e=Object.keys(R.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!L(o))return;const r={};for(const a of e)r[a]=o[a];(B(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=Vr(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&Hr(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,a);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new Nr(u,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return at.add(this._chart,s),!0}}function Hr(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function Vr(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Zi(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function $r(i,t,e){if(e===!1)return!1;const s=Zi(i,e),n=Zi(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Wr(i){let t,e,s,n;return L(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function bn(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Ji(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,s.all)continue;break}c=i.values[l],$(c)&&(o||t===0||At(t)===At(c))&&(t+=c)}return!h&&!s.all?0:t}function jr(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function qe(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Ur(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function qr(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Yr(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function ts(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function es(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=Ur(o,r,s),d=t.length;let u;for(let f=0;f<d;++f){const g=t[f],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});u=b[c]=Yr(n,h,p),u[a]=m,u._top=ts(u,r,!0,s.type),u._bottom=ts(u,r,!1,s.type);const y=u._visualValues||(u._visualValues={});y[a]=m}}function Ye(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Xr(i,t){return Ct(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Gr(i,t,e){return Ct(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function zt(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Xe=i=>i==="reset"||i==="none",is=(i,t)=>t?i:Object.assign({},i),Kr=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:bn(e,!0),values:null};class ve{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=qe(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&zt(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,g)=>d==="x"?u:d==="r"?g:f,o=e.xAxisID=D(s.xAxisID,Ye(t,"x")),r=e.yAxisID=D(s.yAxisID,Ye(t,"y")),a=e.rAxisID=D(s.rAxisID,Ye(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Bi(this._data,this),t._stacked&&zt(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(L(e)){const n=this._cachedMeta;this._data=jr(e,n)}else if(s!==e){if(s){Bi(s,this);const n=this._cachedMeta;zt(n),n._parsed=[]}e&&Object.isExtensible(e)&&Eo(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=qe(e.vScale,e),e.stack!==s.stack&&(n=!0,zt(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(es(this,e._parsed),e._stacked=qe(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{B(n[t])?u=this.parseArrayData(s,n,t,e):L(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);const f=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);s._sorted=l}r&&es(this,u)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let u,f,g;for(u=0,f=n;u<f;++u)g=u+s,d[u]={[a]:h||o.parse(c[g],g),[l]:r.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],c[h]={x:o.parse(Le(f,a),u),y:r.parse(Le(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:bn(n,!0),values:e._stacks[t.axis]._visualValues};return Ji(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Ji(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=Kr(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=qr(a);let u,f;function g(){f=n[u];const p=f[a.axis];return!$(f[t.axis])||h>p||d<p}for(u=0;u<r&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=r-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],$(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Wr(D(this.options.clip,$r(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=Gr(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Xr(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Pe(s);if(a)return is(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(R.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(u,f,g,d);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(is(p,l))),p}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,s,e))}const c=new mn(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Xe(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){Xe(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Xe(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&zt(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}S(ve,"defaults",{}),S(ve,"datasetElementType",null),S(ve,"dataElementType",null);function _t(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Ci{constructor(t){S(this,"options");this.options=t||{}}static override(t){Object.assign(Ci.prototype,t)}init(){}formats(){return _t()}parse(){return _t()}format(){return _t()}add(){return _t()}diff(){return _t()}startOf(){return _t()}endOf(){return _t()}}var Qr={_date:Ci};function Zr(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?Oo:ni;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const u=c(o,t,e-d),f=c(o,t,e+d);return{lo:u.lo,hi:f.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:u}=i,f=u.slice(0,h.lo+1).reverse().findIndex(p=>!I(p[d.axis]));h.lo-=Math.max(0,f);const g=u.slice(h.hi).findIndex(p=>!I(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function He(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:u}=Zr(o[a],t,r,n);for(let f=d;f<=u;++f){const g=h[f];g.skip||s(g,c,f)}}}function Jr(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Ge(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||He(i,e,t,function(a,l,c){!n&&!Zt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function ta(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=Lo(r,{x:t.x,y:t.y});Gs(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return He(i,e,t,o),n}function ea(i,t,e,s,n,o){let r=[];const a=Jr(e);let l=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,n);if(s&&!f)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!f)return;const m=a(t,g);m<l?(r=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:u})}return He(i,e,t,c),r}function Ke(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?ta(i,t,e,n):ea(i,t,e,s,n,o)}function ss(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return He(i,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var ia={modes:{index(i,t,e,s){const n=vt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?Ge(i,n,o,s,r):Ke(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=vt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?Ge(i,n,o,s,r):Ke(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=vt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Ge(i,n,o,s,r)},nearest(i,t,e,s){const n=vt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Ke(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=vt(t,i);return ss(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=vt(t,i);return ss(i,n,"y",e.intersect,s)}}};const yn=["left","top","right","bottom"];function Nt(i,t){return i.filter(e=>e.pos===t)}function ns(i,t){return i.filter(e=>yn.indexOf(e.pos)===-1&&e.box.axis===t)}function Ht(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function sa(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function na(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!yn.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function oa(i,t){const e=na(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function ra(i){const t=sa(i),e=Ht(t.filter(c=>c.box.fullSize),!0),s=Ht(Nt(t,"left"),!0),n=Ht(Nt(t,"right")),o=Ht(Nt(t,"top"),!0),r=Ht(Nt(t,"bottom")),a=ns(t,"x"),l=ns(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Nt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function os(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function xn(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function aa(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!L(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&xn(r,o.getPadding());const a=Math.max(0,t.outerWidth-os(r,i,"left","right")),l=Math.max(0,t.outerHeight-os(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function la(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function ca(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function jt(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,ca(a.horizontal,t));const{same:d,other:u}=aa(t,e,a,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(a)}return c&&jt(n,t,e,s)||h}function pe(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function rs(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,u=c.size||l.height;Pe(c.start)&&(r=c.start),l.fullSize?pe(l,n.left,r,e.outerWidth-n.right-n.left,u):pe(l,t.left+c.placed,r,d,u),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,u=c.size||l.width;Pe(c.start)&&(o=c.start),l.fullSize?pe(l,o,n.top,u,e.outerHeight-n.bottom-n.top):pe(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var G={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=K(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=ra(i.boxes),l=a.vertical,c=a.horizontal;O(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),u=Object.assign({},n);xn(u,K(s));const f=Object.assign({maxPadding:u,w:o,h:r,x:n.left,y:n.top},n),g=oa(l.concat(c),d);jt(a.fullSize,f,d,g),jt(l,f,d,g),jt(c,f,d,g)&&jt(l,f,d,g),la(f),rs(a.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,rs(a.rightAndBottom,f,d,g),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},O(a.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class _n{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class ha extends _n{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const we="$chartjs",da={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},as=i=>i===null||i==="";function ua(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[we]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",as(n)){const o=Yi(i,"width");o!==void 0&&(i.width=o)}if(as(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Yi(i,"height");o!==void 0&&(i.height=o)}return i}const vn=Sr?{passive:!0}:!1;function fa(i,t,e){i&&i.addEventListener(t,e,vn)}function ga(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,vn)}function pa(i,t){const e=da[i.type]||i.type,{x:s,y:n}=vt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Ie(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function ma(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Ie(a.addedNodes,s),r=r&&!Ie(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function ba(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Ie(a.removedNodes,s),r=r&&!Ie(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const te=new Map;let ls=0;function wn(){const i=window.devicePixelRatio;i!==ls&&(ls=i,te.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function ya(i,t){te.size||window.addEventListener("resize",wn),te.set(i,t)}function xa(i){te.delete(i),te.size||window.removeEventListener("resize",wn)}function _a(i,t,e){const s=i.canvas,n=s&&Si(s);if(!n)return;const o=Zs((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),ya(i,o),r}function Qe(i,t,e){e&&e.disconnect(),t==="resize"&&xa(i)}function va(i,t,e){const s=i.canvas,n=Zs(o=>{i.ctx!==null&&e(pa(o,i))},i);return fa(s,t,n),n}class wa extends _n{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(ua(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[we])return!1;const s=e[we].initial;["height","width"].forEach(o=>{const r=s[o];I(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[we],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:ma,detach:ba,resize:_a}[e]||va;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Qe,detach:Qe,resize:Qe}[e]||ga)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return kr(t,e,s,n)}isAttached(t){const e=t&&Si(t);return!!(e&&e.isConnected)}}function Ma(i){return!ki()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?ha:wa}class ot{constructor(){S(this,"x");S(this,"y");S(this,"active",!1);S(this,"options");S(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Oe(this.x)&&Oe(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}S(ot,"defaults",{}),S(ot,"defaultRoutes");function ka(i,t){const e=i.options.ticks,s=Sa(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?Da(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return La(t,c,o,r/n),c;const h=Ca(o,t,n);if(r>0){let d,u;const f=r>1?Math.round((l-a)/(r-1)):null;for(me(t,c,h,I(f)?0:a-f,a),d=0,u=r-1;d<u;d++)me(t,c,h,o[d],o[d+1]);return me(t,c,h,l,I(f)?t.length:l+f),c}return me(t,c,h),c}function Sa(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Ca(i,t,e){const s=Pa(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Mo(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function Da(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function La(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function me(i,t,e,s,n){const o=D(s,0),r=Math.min(D(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function Pa(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const Ta=i=>i==="left"?"right":i==="right"?"left":i,cs=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,hs=(i,t)=>Math.min(t||i,i);function ds(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function Oa(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function Aa(i,t){O(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Vt(i){return i.drawTicks?i.tickLength:0}function us(i,t){if(!i.display)return 0;const e=V(i.font,t),s=K(i.padding);return(B(i.text)?i.text.length:1)*e.lineHeight+s.height}function Ea(i,t){return Ct(i,{scale:t,type:"scale"})}function Ia(i,t,e){return Ct(i,{tick:e,index:t,type:"tick"})}function Fa(i,t,e){let s=xi(i);return(e&&t!=="right"||!e&&t==="right")&&(s=Ta(s)),s}function Ra(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,u,f,g;const p=r-n,m=a-o;if(i.isHorizontal()){if(f=H(s,o,a),L(e)){const b=Object.keys(e)[0],y=e[b];g=h[b].getPixelForValue(y)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=cs(i,e,t);u=a-o}else{if(L(e)){const b=Object.keys(e)[0],y=e[b];f=h[b].getPixelForValue(y)-m+t}else e==="center"?f=(c.left+c.right)/2-m+t:f=cs(i,e,t);g=H(s,r,n),d=e==="left"?-Q:Q}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}class Ft extends ot{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=et(t,Number.POSITIVE_INFINITY),e=et(e,Number.NEGATIVE_INFINITY),s=et(s,Number.POSITIVE_INFINITY),n=et(n,Number.NEGATIVE_INFINITY),{min:et(t,s),max:et(e,n),minDefined:$(t),maxDefined:$(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:et(e,et(s,e)),max:et(s,et(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){E(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=ir(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?ds(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=ka(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){E(this.options.afterUpdate,[this])}beforeSetDimensions(){E(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){E(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),E(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){E(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=E(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){E(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){E(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=hs(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=Z(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:f/(s-1),d+6>a&&(a=f/(s-(t.offset?.5:1)),l=this.maxHeight-Vt(t.grid)-e.padding-us(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),r=Do(Math.min(Math.asin(Z((h.highest.height+6)/a,-1,1)),Math.asin(Z(l/c,-1,1))-Math.asin(Z(u/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){E(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){E(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=us(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Vt(o)+l):(t.height=this.maxHeight,t.width=Vt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,g=Mt(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const b=s.mirror?0:m*d.width+p*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=s.mirror?0:p*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){E(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)I(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=ds(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/hs(e,s));let c=0,h=0,d,u,f,g,p,m,b,y,_,v,x;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},y=p.lineHeight,_=v=0,!I(g)&&!B(g))_=$i(n,b.data,b.gc,_,g),v=y;else if(B(g))for(u=0,f=g.length;u<f;++u)x=g[u],!I(x)&&!B(x)&&(_=$i(n,b.data,b.gc,_,x),v+=y);r.push(_),a.push(v),c=Math.max(_,c),h=Math.max(v,h)}Aa(o,e);const M=r.indexOf(c),k=a.indexOf(h),w=C=>({width:r[C]||0,height:a[C]||0});return{first:w(0),last:w(e-1),widest:w(M),highest:w(k),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return To(this._alignToPixels?xt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=Ia(this.getContext(),t,s))}return this.$context||(this.$context=Ea(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=Mt(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=Vt(o),f=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(N){return xt(s,N,p)};let y,_,v,x,M,k,w,C,A,P,T,W;if(r==="top")y=b(this.bottom),k=this.bottom-u,C=y-m,P=b(t.top)+m,W=t.bottom;else if(r==="bottom")y=b(this.top),P=t.top,W=b(t.bottom)-m,k=y+m,C=this.top+u;else if(r==="left")y=b(this.right),M=this.right-u,w=y-m,A=b(t.left)+m,T=t.right;else if(r==="right")y=b(this.left),A=t.left,T=b(t.right)-m,M=y+m,w=this.left+u;else if(e==="x"){if(r==="center")y=b((t.top+t.bottom)/2+.5);else if(L(r)){const N=Object.keys(r)[0],q=r[N];y=b(this.chart.scales[N].getPixelForValue(q))}P=t.top,W=t.bottom,k=y+m,C=k+u}else if(e==="y"){if(r==="center")y=b((t.left+t.right)/2);else if(L(r)){const N=Object.keys(r)[0],q=r[N];y=b(this.chart.scales[N].getPixelForValue(q))}M=y-m,w=M-u,A=t.left,T=t.right}const tt=D(n.ticks.maxTicksLimit,d),F=Math.max(1,Math.ceil(d/tt));for(_=0;_<d;_+=F){const N=this.getContext(_),q=o.setContext(N),se=a.setContext(N),ne=q.lineWidth,Dt=q.color,oe=se.dash||[],Lt=se.dashOffset,Rt=q.tickWidth,mt=q.tickColor,Bt=q.tickBorderDash||[],bt=q.tickBorderDashOffset;v=Oa(this,_,l),v!==void 0&&(x=xt(s,v,ne),c?M=w=A=T=x:k=C=P=W=x,f.push({tx1:M,ty1:k,tx2:w,ty2:C,x1:A,y1:P,x2:T,y2:W,width:ne,color:Dt,borderDash:oe,borderDashOffset:Lt,tickWidth:Rt,tickColor:mt,tickBorderDash:Bt,tickBorderDashOffset:bt}))}return this._ticksLength=d,this._borderValue=y,f}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=Vt(s.grid),f=u+h,g=d?-h:f,p=-Mt(this.labelRotation),m=[];let b,y,_,v,x,M,k,w,C,A,P,T,W="middle";if(n==="top")M=this.bottom-g,k=this._getXAxisLabelAlignment();else if(n==="bottom")M=this.top+g,k=this._getXAxisLabelAlignment();else if(n==="left"){const F=this._getYAxisLabelAlignment(u);k=F.textAlign,x=F.x}else if(n==="right"){const F=this._getYAxisLabelAlignment(u);k=F.textAlign,x=F.x}else if(e==="x"){if(n==="center")M=(t.top+t.bottom)/2+f;else if(L(n)){const F=Object.keys(n)[0],N=n[F];M=this.chart.scales[F].getPixelForValue(N)+f}k=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-f;else if(L(n)){const F=Object.keys(n)[0],N=n[F];x=this.chart.scales[F].getPixelForValue(N)}k=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(l==="start"?W="top":l==="end"&&(W="bottom"));const tt=this._getLabelSizes();for(b=0,y=a.length;b<y;++b){_=a[b],v=_.label;const F=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,C=this._resolveTickFontOptions(b),A=C.lineHeight,P=B(v)?v.length:1;const N=P/2,q=F.color,se=F.textStrokeColor,ne=F.textStrokeWidth;let Dt=k;r?(x=w,k==="inner"&&(b===y-1?Dt=this.options.reverse?"left":"right":b===0?Dt=this.options.reverse?"right":"left":Dt="center"),n==="top"?c==="near"||p!==0?T=-P*A+A/2:c==="center"?T=-tt.highest.height/2-N*A+A:T=-tt.highest.height+A/2:c==="near"||p!==0?T=A/2:c==="center"?T=tt.highest.height/2-N*A:T=tt.highest.height-P*A,d&&(T*=-1),p!==0&&!F.showLabelBackdrop&&(x+=A/2*Math.sin(p))):(M=w,T=(1-P)*A/2);let oe;if(F.showLabelBackdrop){const Lt=K(F.backdropPadding),Rt=tt.heights[b],mt=tt.widths[b];let Bt=T-Lt.top,bt=0-Lt.left;switch(W){case"middle":Bt-=Rt/2;break;case"bottom":Bt-=Rt;break}switch(k){case"center":bt-=mt/2;break;case"right":bt-=mt;break;case"inner":b===y-1?bt-=mt:b>0&&(bt-=mt/2);break}oe={left:bt,top:Bt,width:mt+Lt.width,height:Rt+Lt.height,color:F.backdropColor}}m.push({label:v,font:C,textOffset:T,options:{rotation:p,color:q,strokeColor:se,strokeWidth:ne,textAlign:Dt,textBaseline:W,translation:[x,M],backdrop:oe}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-Mt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=xt(t,this.left,r)-r/2,h=xt(t,this.right,a)+a/2,d=u=l):(d=xt(t,this.top,r)-r/2,u=xt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Be(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Jt(s,c,0,h,l,a)}n&&ze(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=V(s.font),r=K(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||L(e)?(l+=r.bottom,B(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=Ra(this,l,e,a);Jt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:Fa(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=D(t.grid&&t.grid.z,-1),n=D(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Ft.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return V(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class be{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;Na(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,Ba(t,r,s),this.override&&R.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in R[n]&&(delete R[n][s],this.override&&delete St[s])}}function Ba(i,t,e){const s=Qt(Object.create(null),[e?R.get(e):{},R.get(t),i.defaults]);R.set(t,s),i.defaultRoutes&&za(t,i.defaultRoutes),i.descriptors&&R.describe(t,i.descriptors)}function za(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");R.route(o,n,l,a)})}function Na(i){return"id"in i&&"defaults"in i}class Ha{constructor(){this.controllers=new be(ve,"datasets",!0),this.elements=new be(ot,"elements"),this.plugins=new be(Object,"plugins"),this.scales=new be(Ft,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):O(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=bi(t);E(s["before"+n],[],s),e[t](s),E(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var st=new Ha;class Va{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(E(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){I(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=D(s.options&&s.options.plugins,{}),o=$a(s);return n===!1&&!e?[]:ja(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function $a(i){const t={},e=[],s=Object.keys(st.plugins.items);for(let o=0;o<s.length;o++)e.push(st.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function Wa(i,t){return!t&&i===!1?null:i===!0?{}:i}function ja(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=Wa(s[l],n);c!==null&&o.push({plugin:a,options:Ua(i.config,{plugin:a,local:e[l]},c,r)})}return o}function Ua(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ai(i,t){const e=R.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function qa(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function Ya(i,t){return i===t?"_index_":"_value_"}function fs(i){if(i==="x"||i==="y"||i==="r")return i}function Xa(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function li(i,...t){if(fs(i))return i;for(const e of t){const s=e.axis||Xa(e.position)||i.length>1&&fs(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function gs(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function Ga(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return gs(i,"x",e[0])||gs(i,"y",e[0])}return{}}function Ka(i,t){const e=St[i.type]||{scales:{}},s=t.scales||{},n=ai(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!L(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=li(r,a,Ga(r,i),R.scales[a.type]),c=Ya(l,n),h=e.scales||{};o[r]=qt(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||ai(a,t),h=(St[a]||{}).scales||{};Object.keys(h).forEach(d=>{const u=qa(d,l),f=r[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),qt(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];qt(a,[R.scales[a.type],R.scale])}),o}function Mn(i){const t=i.options||(i.options={});t.plugins=D(t.plugins,{}),t.scales=Ka(i,t)}function kn(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Qa(i){return i=i||{},i.data=kn(i.data),Mn(i),i}const ps=new Map,Sn=new Set;function ye(i,t){let e=ps.get(i);return e||(e=t(),ps.set(i,e),Sn.add(e)),e}const $t=(i,t,e)=>{const s=Le(t,e);s!==void 0&&i.add(s)};class Za{constructor(t){this._config=Qa(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=kn(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Mn(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return ye(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return ye(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return ye(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return ye(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>$t(l,t,d))),h.forEach(d=>$t(l,n,d)),h.forEach(d=>$t(l,St[o]||{},d)),h.forEach(d=>$t(l,R,d)),h.forEach(d=>$t(l,oi,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Sn.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,St[e]||{},R.datasets[e]||{},{type:e},R,oi]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=ms(this._resolverCache,t,n);let l=r;if(tl(r,e)){o.$shared=!1,s=pt(s)?s():s;const c=this.createResolver(t,s,a);l=Et(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=ms(this._resolverCache,t,s);return L(e)?Et(o,e,void 0,n):o}}function ms(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:vi(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const Ja=i=>L(i)&&Object.getOwnPropertyNames(i).some(t=>pt(i[t]));function tl(i,t){const{isScriptable:e,isIndexable:s}=on(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(pt(a)||Ja(a))||r&&B(a))return!0}return!1}var el="4.5.0";const il=["top","bottom","left","right","chartArea"];function bs(i,t){return i==="top"||i==="bottom"||il.indexOf(i)===-1&&t==="x"}function ys(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function xs(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),E(e&&e.onComplete,[i],t)}function sl(i){const t=i.chart,e=t.options.animation;E(e&&e.onProgress,[i],t)}function Cn(i){return ki()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const Me={},_s=i=>{const t=Cn(i);return Object.values(Me).filter(e=>e.canvas===t).pop()};function nl(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function ol(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}class X{static register(...t){st.add(...t),vs()}static unregister(...t){st.remove(...t),vs()}constructor(t,e){const s=this.config=new Za(e),n=Cn(t),o=_s(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Ma(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=go(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Va,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Fo(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],Me[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}at.listen(this,"complete",xs),at.listen(this,"progress",sl),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return I(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return st}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():qi(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Wi(this.canvas,this.ctx),this}stop(){return at.stop(this),this}resize(t,e){at.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,qi(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),E(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};O(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=li(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),O(o,r=>{const a=r.options,l=a.id,c=li(l,a),h=D(a.type,r.dtype);(a.position===void 0||bs(a.position,c)!==bs(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const u=st.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),O(n,(r,a)=>{r||delete s[a]}),O(s,r=>{G.configure(this,r,r.options),G.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(ys("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||ai(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=st.getController(a),{datasetElementType:c,dataElementType:h}=R.datasets[a];Object.assign(l,{dataElementType:st.getElement(h),datasetElementType:c&&st.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){O(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||O(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(ys("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){O(this.scales,t=>{G.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!Ei(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;nl(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!Ei(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;G.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],O(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,pt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(at.has(this)?this.attached&&!at.running(this)&&at.start(this):(this.draw(),xs({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=pn(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&Be(e,n),t.controller.draw(),n&&ze(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return Zt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=ia.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Ct(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Pe(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),at.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Wi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete Me[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};O(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){O(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},O(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!Ce(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=_o(t),c=ol(t,this._lastEvent,s,l);s&&(this._lastEvent=null,E(o.onHover,[t,a,this],this),l&&E(o.onClick,[t,a,this],this));const h=!Ce(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}S(X,"defaults",R),S(X,"instances",Me),S(X,"overrides",St),S(X,"registry",st),S(X,"version",el),S(X,"getChart",_s);function vs(){return O(X.instances,i=>i._plugins.invalidate())}function Dn(i,t,e=t){i.lineCap=D(e.borderCapStyle,t.borderCapStyle),i.setLineDash(D(e.borderDash,t.borderDash)),i.lineDashOffset=D(e.borderDashOffset,t.borderDashOffset),i.lineJoin=D(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=D(e.borderWidth,t.borderWidth),i.strokeStyle=D(e.borderColor,t.borderColor)}function rl(i,t,e){i.lineTo(e.x,e.y)}function al(i){return i.stepped?Yo:i.tension||i.cubicInterpolationMode==="monotone"?Xo:rl}function Ln(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function ll(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=Ln(n,e,s),h=al(o);let{move:d=!0,reverse:u}=s||{},f,g,p;for(f=0;f<=c;++f)g=n[(a+(u?c-f:f))%r],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,p,g,u,o.stepped),p=g);return l&&(g=n[(a+(u?c:0))%r],h(i,p,g,u,o.stepped)),!!l}function cl(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=Ln(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,u,f,g,p,m,b;const y=v=>(r+(c?a-v:v))%o,_=()=>{p!==m&&(i.lineTo(h,m),i.lineTo(h,p),i.lineTo(h,b))};for(l&&(f=n[y(0)],i.moveTo(f.x,f.y)),u=0;u<=a;++u){if(f=n[y(u)],f.skip)continue;const v=f.x,x=f.y,M=v|0;M===g?(x<p?p=x:x>m&&(m=x),h=(d*h+v)/++d):(_(),i.lineTo(v,x),g=M,d=0,p=m=x),b=x}_()}function ci(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?cl:ll}function hl(i){return i.stepped?Cr:i.tension||i.cubicInterpolationMode==="monotone"?Dr:wt}function dl(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),Dn(i,t.options),i.stroke(n)}function ul(i,t,e,s){const{segments:n,options:o}=t,r=ci(t);for(const a of n)Dn(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const fl=typeof Path2D=="function";function gl(i,t,e,s){fl&&!t.options.segment?dl(i,t,e,s):ul(i,t,e,s)}class ut extends ot{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;yr(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Er(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=gn(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=hl(s);let c,h;for(c=0,h=r.length;c<h;++c){const{start:d,end:u}=r[c],f=o[d],g=o[u];if(f===g){a.push(f);continue}const p=Math.abs((n-f[e])/(g[e]-f[e])),m=l(f,g,p,s.stepped);m[e]=t[e],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,e,s){return ci(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=ci(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),gl(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}S(ut,"id","line"),S(ut,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),S(ut,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),S(ut,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function ws(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class ke extends ot{constructor(e){super();S(this,"parsed");S(this,"skip");S(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(e-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return ws(this,e,"x",s)}inYRange(e,s){return ws(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!Zt(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,ri(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}S(ke,"id","point"),S(ke,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),S(ke,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Pn(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function ft(i,t,e,s){return i?0:Z(t,e,s)}function pl(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=nn(s);return{t:ft(n.top,o.top,0,e),r:ft(n.right,o.right,0,t),b:ft(n.bottom,o.bottom,0,e),l:ft(n.left,o.left,0,t)}}function ml(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Tt(n),r=Math.min(t,e),a=i.borderSkipped,l=s||L(n);return{topLeft:ft(!l||a.top||a.left,o.topLeft,0,r),topRight:ft(!l||a.top||a.right,o.topRight,0,r),bottomLeft:ft(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:ft(!l||a.bottom||a.right,o.bottomRight,0,r)}}function bl(i){const t=Pn(i),e=t.right-t.left,s=t.bottom-t.top,n=pl(i,e/2,s/2),o=ml(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function Ze(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&Pn(i,s);return a&&(n||dt(t,a.left,a.right))&&(o||dt(e,a.top,a.bottom))}function yl(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function xl(i,t){i.rect(t.x,t.y,t.w,t.h)}function Je(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class Se extends ot{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=bl(this),a=yl(r.radius)?Ae:xl;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,Je(r,e,o)),t.clip(),a(t,Je(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,Je(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return Ze(this,t,e,s)}inXRange(t,e){return Ze(this,t,null,e)}inYRange(t,e){return Ze(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}S(Se,"id","bar"),S(Se,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),S(Se,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function _l(i,t,e){const s=i.segments,n=i.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=Ve(l,c,n);const h=hi(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}const d=gn(t,h);for(const u of d){const f=hi(e,o[u.start],o[u.end],u.loop),g=fn(a,n,f);for(const p of g)r.push({source:p,target:u,start:{[e]:Ms(h,f,"start",Math.max)},end:{[e]:Ms(h,f,"end",Math.min)}})}}return r}function hi(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=nt(n),o=nt(o)),{property:i,start:n,end:o}}function vl(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=Ve(r,a,n);const l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function Ve(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function Ms(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function Tn(i,t){let e=[],s=!1;return B(i)?(s=!0,e=i):e=vl(i,t),e.length?new ut({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function ks(i){return i&&i.fill!==!1}function wl(i,t,e){let n=i[t].fill;const o=[t];let r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!$(n))return n;if(r=i[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function Ml(i,t,e){const s=Dl(i);if(L(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return $(n)&&Math.floor(n)===n?kl(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function kl(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function Sl(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:L(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function Cl(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:L(i)?s=i.value:s=t.getBaseValue(),s}function Dl(i){const t=i.options,e=t.fill;let s=D(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Ll(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,r=s.points,a=Pl(t,e);a.push(Tn({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)Tl(n,r[h],a)}return new ut({points:n,options:{}})}function Pl(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function Tl(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:r,last:a,point:l}=Ol(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(i.push(l),!a)break}}i.push(...s)}function Ol(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,r=i.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=r[h.start][e],u=r[h.end][e];if(dt(n,d,u)){a=n===d,l=n===u;break}}return{first:a,last:l,point:s}}class On{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:r}=this;return e=e||{start:0,end:J},t.arc(n,o,r,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function Al(i){const{chart:t,fill:e,line:s}=i;if($(e))return El(t,e);if(e==="stack")return Ll(i);if(e==="shape")return!0;const n=Il(i);return n instanceof On?n:Tn(n,s)}function El(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function Il(i){return(i.scale||{}).getPointPositionForValue?Rl(i):Fl(i)}function Fl(i){const{scale:t={},fill:e}=i,s=Sl(e,t);if($(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function Rl(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,r=Cl(e,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new On({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function ti(i,t,e){const s=Al(t),{chart:n,index:o,line:r,scale:a,axis:l}=t,c=r.options,h=c.fill,d=c.backgroundColor,{above:u=d,below:f=d}=h||{},g=n.getDatasetMeta(o),p=pn(n,g);s&&r.points.length&&(Be(i,e),Bl(i,{line:r,target:s,above:u,below:f,area:e,scale:a,axis:l,clip:p}),ze(i))}function Bl(i,t){const{line:e,target:s,above:n,below:o,area:r,scale:a,clip:l}=t,c=e._loop?"angle":t.axis;i.save();let h=o;o!==n&&(c==="x"?(Ss(i,s,r.top),ei(i,{line:e,target:s,color:n,scale:a,property:c,clip:l}),i.restore(),i.save(),Ss(i,s,r.bottom)):c==="y"&&(Cs(i,s,r.left),ei(i,{line:e,target:s,color:o,scale:a,property:c,clip:l}),i.restore(),i.save(),Cs(i,s,r.right),h=n)),ei(i,{line:e,target:s,color:h,scale:a,property:c,clip:l}),i.restore()}function Ss(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[Ve(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function Cs(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[Ve(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(e,h.y),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(e,d.y)}i.lineTo(e,t.first().y),i.closePath(),i.clip()}function ei(i,t){const{line:e,target:s,property:n,color:o,scale:r,clip:a}=t,l=_l(e,s,n);for(const{source:c,target:h,start:d,end:u}of l){const{style:{backgroundColor:f=o}={}}=c,g=s!==!0;i.save(),i.fillStyle=f,zl(i,r,a,g&&hi(n,d,u)),i.beginPath();const p=!!e.pathSegment(i,c);let m;if(g){p?i.closePath():Ds(i,s,u,n);const b=!!s.pathSegment(i,h,{move:p,reverse:!0});m=p&&b,m||Ds(i,s,d,n)}i.closePath(),i.fill(m?"evenodd":"nonzero"),i.restore()}}function zl(i,t,e,s){const n=t.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=r,c=n.top,h=a,d=n.bottom):(l=n.left,c=r,h=n.right,d=a),i.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),d=Math.min(d,e.bottom)),i.rect(l,c,h-l,d-c),i.clip()}}function Ds(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var Nl={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,r,a,l;for(r=0;r<s;++r)o=i.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof ut&&(l={visible:i.isDatasetVisible(r),index:r,fill:Ml(a,r,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=wl(n,r,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let r=n.length-1;r>=0;--r){const a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&ti(i.ctx,a,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;ks(o)&&ti(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!ks(s)||e.drawTime!=="beforeDatasetDraw"||ti(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const Ls=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},Hl=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class Ps extends ot{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=E(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=V(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Ls(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{const m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:y,itemHeight:_}=Vl(s,e,o,m,n);b>0&&f+_+2*a>h&&(d+=u+a,c.push({width:u,height:f}),g+=u+a,p++,u=f=0),l[b]={left:g,top:f,col:p,width:y,height:_},u=Math.max(u,y),f+=_+a}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Ot(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=H(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=H(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=H(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=H(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Be(t,this),this._draw(),ze(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=R.color,l=Ot(t.rtl,this.left,this.width),c=V(r.font),{padding:h}=r,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=Ls(r,d),b=function(M,k,w){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const C=D(w.lineWidth,1);if(n.fillStyle=D(w.fillStyle,a),n.lineCap=D(w.lineCap,"butt"),n.lineDashOffset=D(w.lineDashOffset,0),n.lineJoin=D(w.lineJoin,"miter"),n.lineWidth=C,n.strokeStyle=D(w.strokeStyle,a),n.setLineDash(D(w.lineDash,[])),r.usePointStyle){const A={radius:p*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:C},P=l.xPlus(M,g/2),T=k+u;en(n,A,P,T,r.pointStyleWidth&&g)}else{const A=k+Math.max((d-p)/2,0),P=l.leftForLtr(M,g),T=Tt(w.borderRadius);n.beginPath(),Object.values(T).some(W=>W!==0)?Ae(n,{x:P,y:A,w:g,h:p,radius:T}):n.rect(P,A,g,p),n.fill(),C!==0&&n.stroke()}n.restore()},y=function(M,k,w){Jt(n,w.text,M,k+m/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},_=this.isHorizontal(),v=this._computeTitleHeight();_?f={x:H(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:f={x:this.left+h,y:H(o,this.top+v+h,this.bottom-e[0].height),line:0},hn(this.ctx,t.textDirection);const x=m+h;this.legendItems.forEach((M,k)=>{n.strokeStyle=M.fontColor,n.fillStyle=M.fontColor;const w=n.measureText(M.text).width,C=l.textAlign(M.textAlign||(M.textAlign=r.textAlign)),A=g+u+w;let P=f.x,T=f.y;l.setWidth(this.width),_?k>0&&P+A+h>this.right&&(T=f.y+=x,f.line++,P=f.x=H(o,this.left+h,this.right-s[f.line])):k>0&&T+x>this.bottom&&(P=f.x=P+e[f.line].width+h,f.line++,T=f.y=H(o,this.top+v+h,this.bottom-e[f.line].height));const W=l.x(P);if(b(W,T,M),P=Ro(C,P+g+u,_?P+A:this.right,t.rtl),y(l.x(P),T,M),_)f.x+=A+h;else if(typeof M.text!="string"){const tt=c.lineHeight;f.y+=An(M,tt)+h}else f.y+=x}),dn(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=V(e.font),n=K(e.padding);if(!e.display)return;const o=Ot(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=H(t.align,d,this.right-u);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+H(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const f=H(a,d,d+u);r.textAlign=o.textAlign(xi(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Jt(r,e.text,f,h,s)}_computeTitleHeight(){const t=this.options.title,e=V(t.font),s=K(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(dt(t,this.left,this.right)&&dt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],dt(t,n.left,n.left+n.width)&&dt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!jl(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=Hl(n,s);n&&!o&&E(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&E(e.onHover,[t,s,this],this)}else s&&E(e.onClick,[t,s,this],this)}}function Vl(i,t,e,s,n){const o=$l(s,i,t,e),r=Wl(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function $l(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function Wl(i,t,e){let s=i;return typeof t.text!="string"&&(s=An(t,e)),s}function An(i,t){const e=i.text?i.text.length:0;return t*e}function jl(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Ul={id:"legend",_element:Ps,start(i,t,e){const s=i.legend=new Ps({ctx:i.ctx,options:e,chart:i});G.configure(i,s,e),G.addBox(i,s)},stop(i){G.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;G.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=K(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class En extends ot{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=B(s.text)?s.text.length:1;this._padding=K(s.padding);const o=n*V(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=H(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=H(a,n,e),l=z*-.5):(h=o-t,d=H(a,e,n),l=z*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=V(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Jt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:xi(e.align),textBaseline:"middle",translation:[r,a]})}}function ql(i,t){const e=new En({ctx:i.ctx,options:t,chart:i});G.configure(i,e,t),G.addBox(i,e),i.titleBlock=e}var Yl={id:"title",_element:En,start(i,t,e){ql(i,e)},stop(i){const t=i.titleBlock;G.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;G.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Ut={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=si(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function it(i,t){return t&&(B(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function lt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function Xl(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function Ts(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=V(t.bodyFont),c=V(t.titleFont),h=V(t.footerFont),d=o.length,u=n.length,f=s.length,g=K(t.padding);let p=g.height,m=0,b=s.reduce((v,x)=>v+x.before.length+x.lines.length+x.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=f*v+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(p+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let y=0;const _=function(v){m=Math.max(m,e.measureText(v).width+y)};return e.save(),e.font=c.string,O(i.title,_),e.font=l.string,O(i.beforeBody.concat(i.afterBody),_),y=t.displayColors?r+2+t.boxPadding:0,O(s,v=>{O(v.before,_),O(v.lines,_),O(v.after,_)}),y=0,e.font=h.string,O(i.footer,_),e.restore(),m+=g.width,{width:m,height:p}}function Gl(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function Kl(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function Ql(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Kl(c,i,t,e)&&(c="center"),c}function Os(i,t,e){const s=e.yAlign||t.yAlign||Gl(i,e);return{xAlign:e.xAlign||t.xAlign||Ql(i,t,e,s),yAlign:s}}function Zl(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function Jl(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function As(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Tt(r);let g=Zl(t,a);const p=Jl(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,u)+n:a==="right"&&(g+=Math.max(d,f)+n),{x:Z(g,0,s.width-t.width),y:Z(p,0,s.height-t.height)}}function xe(i,t,e){const s=K(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function Es(i){return it([],lt(i))}function tc(i,t,e){return Ct(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function Is(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const In={beforeTitle:rt,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:rt,beforeBody:rt,beforeLabel:rt,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return I(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:rt,afterBody:rt,beforeFooter:rt,footer:rt,afterFooter:rt};function j(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?In[t].call(e,s):n}class di extends ot{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new mn(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=tc(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=j(s,"beforeTitle",this,t),o=j(s,"title",this,t),r=j(s,"afterTitle",this,t);let a=[];return a=it(a,lt(n)),a=it(a,lt(o)),a=it(a,lt(r)),a}getBeforeBody(t,e){return Es(j(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return O(t,o=>{const r={before:[],lines:[],after:[]},a=Is(s,o);it(r.before,lt(j(a,"beforeLabel",this,o))),it(r.lines,j(a,"label",this,o)),it(r.after,lt(j(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return Es(j(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=j(s,"beforeFooter",this,t),o=j(s,"footer",this,t),r=j(s,"afterFooter",this,t);let a=[];return a=it(a,lt(n)),a=it(a,lt(o)),a=it(a,lt(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(Xl(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,u)=>t.filter(h,d,u,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),O(a,h=>{const d=Is(t.callbacks,h);n.push(j(d,"labelColor",this,h)),o.push(j(d,"labelPointStyle",this,h)),r.push(j(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=Ut[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=Ts(this,s),c=Object.assign({},a,l),h=Os(this.chart,s,c),d=As(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Tt(a),{x:u,y:f}=t,{width:g,height:p}=e;let m,b,y,_,v,x;return o==="center"?(v=f+p/2,n==="left"?(m=u,b=m-r,_=v+r,x=v-r):(m=u+g,b=m+r,_=v-r,x=v+r),y=m):(n==="left"?b=u+Math.max(l,h)+r:n==="right"?b=u+g-Math.max(c,d)-r:b=this.caretX,o==="top"?(_=f,v=_-r,m=b-r,y=b+r):(_=f+p,v=_+r,m=b+r,y=b-r),x=_),{x1:m,x2:b,x3:y,y1:_,y2:v,y3:x}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=Ot(s.rtl,this.x,this.width);for(t.x=xe(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=V(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=V(o.bodyFont),d=xe(this,"left",o),u=n.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+f;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=n.leftForLtr(u,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,ri(t,p,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,ri(t,p,m,b)}else{t.lineWidth=L(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=n.leftForLtr(u,c),m=n.leftForLtr(n.xPlus(u,1),c-2),b=Tt(r.borderRadius);Object.values(b).some(y=>y!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Ae(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Ae(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=V(s.bodyFont);let u=d.lineHeight,f=0;const g=Ot(s.rtl,this.x,this.width),p=function(w){e.fillText(w,g.x(t.x+f),t.y+u/2),t.y+=u+o},m=g.textAlign(r);let b,y,_,v,x,M,k;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=xe(this,m,s),e.fillStyle=s.bodyColor,O(this.beforeBody,p),f=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,M=n.length;v<M;++v){for(b=n[v],y=this.labelTextColors[v],e.fillStyle=y,O(b.before,p),_=b.lines,a&&_.length&&(this._drawColorBox(e,t,v,g,s),u=Math.max(d.lineHeight,l)),x=0,k=_.length;x<k;++x)p(_[x]),u=d.lineHeight;O(b.after,p)}f=0,u=d.lineHeight,O(this.afterBody,p),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Ot(s.rtl,this.x,this.width);for(t.x=xe(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=V(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:g}=Tt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-u,l),e.quadraticCurveTo(a+c,l,a+c,l+u),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-g),e.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+f,l+h),e.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=Ut[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Ts(this,t),l=Object.assign({},r,this._size),c=Os(e,t,l),h=As(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=K(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),hn(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),dn(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!Ce(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!Ce(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=Ut[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}S(di,"positioners",Ut);var ec={id:"tooltip",_element:di,positioners:Ut,afterInit(i,t,e){e&&(i.tooltip=new di({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:In},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const ic=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function sc(i,t,e,s){const n=i.indexOf(t);if(n===-1)return ic(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const nc=(i,t)=>i===null?null:Z(Math.round(i),0,t);function Fs(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class ui extends Ft{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(I(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:sc(s,t,D(e,t),this._addedLabels),nc(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return Fs.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}S(ui,"id","category"),S(ui,"defaults",{ticks:{callback:Fs}});function oc(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,g=h-1,{min:p,max:m}=t,b=!I(r),y=!I(a),_=!I(c),v=(m-p)/(d+1);let x=Fi((m-p)/g/f)*f,M,k,w,C;if(x<1e-14&&!b&&!y)return[{value:p},{value:m}];C=Math.ceil(m/x)-Math.floor(p/x),C>g&&(x=Fi(C*x/g/f)*f),I(l)||(M=Math.pow(10,l),x=Math.ceil(x*M)/M),n==="ticks"?(k=Math.floor(p/x)*x,w=Math.ceil(m/x)*x):(k=p,w=m),b&&y&&o&&So((a-r)/o,x/1e3)?(C=Math.round(Math.min((a-r)/x,h)),x=(a-r)/C,k=r,w=a):_?(k=b?r:k,w=y?a:w,C=c-1,x=(w-k)/C):(C=(w-k)/x,Yt(C,Math.round(C),x/1e3)?C=Math.round(C):C=Math.ceil(C));const A=Math.max(Ri(x),Ri(k));M=Math.pow(10,I(l)?A:l),k=Math.round(k*M)/M,w=Math.round(w*M)/M;let P=0;for(b&&(u&&k!==r?(e.push({value:r}),k<r&&P++,Yt(Math.round((k+P*x)*M)/M,r,Rs(r,v,i))&&P++):k<r&&P++);P<C;++P){const T=Math.round((k+P*x)*M)/M;if(y&&T>a)break;e.push({value:T})}return y&&u&&w!==a?e.length&&Yt(e[e.length-1].value,a,Rs(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!y||w===a)&&e.push({value:w}),e}function Rs(i,t,{horizontal:e,minRotation:s}){const n=Mt(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class rc extends Ft{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return I(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=At(n),c=At(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=oc(n,o);return t.bounds==="ticks"&&Co(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Js(t,this.chart.options.locale,this.options.ticks.format)}}class fi extends rc{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=$(t)?t:0,this.max=$(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=Mt(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}S(fi,"id","linear"),S(fi,"defaults",{ticks:{callback:tn.formatters.numeric}});const $e={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},U=Object.keys($e);function Bs(i,t){return i-t}function zs(i,t){if(I(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),$(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(Oe(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function Ns(i,t,e,s){const n=U.length;for(let o=U.indexOf(i);o<n-1;++o){const r=$e[U[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return U[o]}return U[n-1]}function ac(i,t,e,s,n){for(let o=U.length-1;o>=U.indexOf(e);o--){const r=U[o];if($e[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return U[e?U.indexOf(e):0]}function lc(i){for(let t=U.indexOf(i)+1,e=U.length;t<e;++t)if($e[U[t]].common)return U[t]}function Hs(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=yi(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function cc(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function Vs(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:cc(i,s,n,e)}class Fe extends Ft{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new Qr._date(t.adapters.date);n.init(e),qt(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:zs(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=$(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=$(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=Ao(n,o,r);return this._unit=e.unit||(s.autoSkip?Ns(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):ac(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:lc(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),Vs(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=Z(e,0,r),s=Z(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||Ns(o.minUnit,e,s,this._getLabelCapacity(e)),a=D(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=Oe(l)||l===!0,h={};let d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,a,r),f++)Hs(h,u,g);return(u===s||n.bounds==="ticks"||f===1)&&Hs(h,u,g),Object.keys(h).sort(Bs).map(p=>+p)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return E(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],u=s[e],f=c&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=Mt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Vs(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(zs(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Io(t.sort(Bs))}}S(Fe,"id","time"),S(Fe,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function _e(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=ni(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=ni(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class $s extends Fe{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=_e(e,this.min),this._tableRange=_e(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(_e(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return _e(this._table,s*this._tableRange+this._minPos,!0)}}S($s,"id","timeseries"),S($s,"defaults",Fe.defaults);X.register(ui,fi,Se,ut,ke,Yl,ec,Ul,Nl);class Fn{constructor(){this.charts=new Map,this.defaultColors=["#3498db","#e74c3c","#2ecc71","#f39c12","#9b59b6","#1abc9c","#34495e","#e67e22","#95a5a6","#f1c40f"]}createHistogram(t,e,s={}){const n=document.getElementById(t);if(!n)throw new Error(`Canvas element with ID '${t}' not found`);this.destroyChart(t);const o={type:"bar",data:{labels:e.labels,datasets:[{label:"Frequency",data:e.bins,backgroundColor:s.backgroundColor||this.defaultColors[0]+"80",borderColor:s.borderColor||this.defaultColors[0],borderWidth:1,barPercentage:.95,categoryPercentage:.95}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:s.title||"Histogram",font:{size:16,weight:"bold"}},legend:{display:s.showLegend!==!1},tooltip:{callbacks:{title:a=>`Range: ${a[0].label}`,label:a=>`Frequency: ${a.parsed.y}`}}},scales:{x:{title:{display:!0,text:s.xAxisLabel||"Value Range"},grid:{display:s.showGrid!==!1}},y:{title:{display:!0,text:s.yAxisLabel||"Frequency"},beginAtZero:!0,grid:{display:s.showGrid!==!1}}},...s.chartOptions}},r=new X(n,o);return this.charts.set(t,r),r}createBoxPlot(t,e,s={}){const n=document.getElementById(t);if(!n)throw new Error(`Canvas element with ID '${t}' not found`);this.destroyChart(t);const o={type:"scatter",data:{datasets:[{label:"Outliers",data:e.outliers.map((a,l)=>({x:1,y:a})),backgroundColor:s.outlierColor||"#e74c3c",borderColor:s.outlierColor||"#e74c3c",pointRadius:4,pointHoverRadius:6}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:s.title||"Box Plot",font:{size:16,weight:"bold"}},legend:{display:s.showLegend!==!1},tooltip:{callbacks:{title:()=>"Box Plot",label:a=>a.datasetIndex===0?`Outlier: ${a.parsed.y.toFixed(2)}`:""}}},scales:{x:{type:"linear",min:.5,max:1.5,display:!1},y:{title:{display:!0,text:s.yAxisLabel||"Value"},min:Math.min(e.min,...e.outliers)*.95,max:Math.max(e.max,...e.outliers)*1.05}},onHover:(a,l)=>{a.native.target.style.cursor=l.length>0?"pointer":"default"}},plugins:[{id:"boxPlotDrawer",afterDatasetsDraw:a=>{this.drawBoxPlot(a,e,s)}}]},r=new X(n,o);return this.charts.set(t,r),r}createVariabilityPlot(t,e,s={}){const n=document.getElementById(t);if(!n)throw new Error(`Canvas element with ID '${t}' not found`);this.destroyChart(t);const o=[];if(o.push({label:s.dataLabel||"Data Points",data:e.raw.map((l,c)=>({x:c+1,y:l})),borderColor:s.lineColor||this.defaultColors[0],backgroundColor:s.lineColor||this.defaultColors[0],fill:!1,tension:.1,pointRadius:3,pointHoverRadius:5}),s.showMean!==!1){const l=Array(e.raw.length).fill(e.statistics.mean);o.push({label:"Mean",data:l.map((c,h)=>({x:h+1,y:c})),borderColor:s.meanColor||"#e74c3c",backgroundColor:s.meanColor||"#e74c3c",borderDash:[5,5],fill:!1,pointRadius:0})}if(s.showConfidenceInterval&&e.confidenceInterval){const l=Array(e.raw.length).fill(e.confidenceInterval.upper),c=Array(e.raw.length).fill(e.confidenceInterval.lower);o.push({label:`${s.confidence||95}% Confidence Interval`,data:l.map((h,d)=>({x:d+1,y:h})),borderColor:s.ciColor||"#95a5a6",backgroundColor:s.ciColor||"#95a5a640",borderDash:[3,3],fill:"+1",pointRadius:0}),o.push({label:"",data:c.map((h,d)=>({x:d+1,y:h})),borderColor:s.ciColor||"#95a5a6",backgroundColor:s.ciColor||"#95a5a640",borderDash:[3,3],fill:!1,pointRadius:0})}const r={type:"line",data:{datasets:o},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:s.title||"Variability Plot",font:{size:16,weight:"bold"}},legend:{display:s.showLegend!==!1},tooltip:{mode:"index",intersect:!1,callbacks:{title:l=>`Data Point ${l[0].label}`,label:l=>`${l.dataset.label}: ${l.parsed.y.toFixed(3)}`}}},scales:{x:{title:{display:!0,text:s.xAxisLabel||"Observation"},grid:{display:s.showGrid!==!1}},y:{title:{display:!0,text:s.yAxisLabel||"Value"},grid:{display:s.showGrid!==!1}}},interaction:{mode:"nearest",axis:"x",intersect:!1}}},a=new X(n,r);return this.charts.set(t,a),a}createScatterPlot(t,e,s={}){const n=document.getElementById(t);if(!n)throw new Error(`Canvas element with ID '${t}' not found`);this.destroyChart(t);const r={type:"scatter",data:{datasets:Object.keys(e).map((l,c)=>{const h=e[l],d=this.defaultColors[c%this.defaultColors.length];return{label:l,data:h.raw?h.raw.map((u,f)=>({x:f+1,y:u})):[],backgroundColor:d+"80",borderColor:d,pointRadius:4,pointHoverRadius:6}})},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:s.title||"Scatter Plot by Group",font:{size:16,weight:"bold"}},legend:{display:s.showLegend!==!1},tooltip:{callbacks:{title:l=>`${l[0].dataset.label} - Point ${l[0].label}`,label:l=>`Value: ${l.parsed.y.toFixed(3)}`}}},scales:{x:{title:{display:!0,text:s.xAxisLabel||"Observation"}},y:{title:{display:!0,text:s.yAxisLabel||"Value"}}}}},a=new X(n,r);return this.charts.set(t,a),a}drawBoxPlot(t,e,s={}){const n=t.ctx,o=t.chartArea,r=t.scales.y,a=60,l=o.left+(o.right-o.left)/2,c=l-a/2,h=l+a/2,d=r.getPixelForValue(e.q1),u=r.getPixelForValue(e.median),f=r.getPixelForValue(e.q3),g=r.getPixelForValue(e.whiskerLow),p=r.getPixelForValue(e.whiskerHigh);n.save(),n.strokeStyle=s.boxColor||"#2c3e50",n.fillStyle=s.boxFillColor||"#ecf0f1",n.lineWidth=2,n.fillRect(c,f,a,d-f),n.strokeRect(c,f,a,d-f),n.beginPath(),n.moveTo(c,u),n.lineTo(h,u),n.lineWidth=3,n.strokeStyle=s.medianColor||"#e74c3c",n.stroke(),n.strokeStyle=s.whiskerColor||"#2c3e50",n.lineWidth=2,n.beginPath(),n.moveTo(l,f),n.lineTo(l,p),n.moveTo(l-15,p),n.lineTo(l+15,p),n.stroke(),n.beginPath(),n.moveTo(l,d),n.lineTo(l,g),n.moveTo(l-15,g),n.lineTo(l+15,g),n.stroke(),n.restore()}destroyChart(t){const e=this.charts.get(t);e&&(e.destroy(),this.charts.delete(t))}destroyAllCharts(){this.charts.forEach(t=>t.destroy()),this.charts.clear()}getChart(t){return this.charts.get(t)||null}exportChart(t,e="png"){const s=this.charts.get(t);if(!s)throw new Error(`Chart with ID '${t}' not found`);return s.toBase64Image(`image/${e}`,1)}getDefaultOptions(t){const e={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}}};switch(t){case"histogram":return{...e,scales:{y:{beginAtZero:!0,title:{display:!0,text:"Frequency"}},x:{title:{display:!0,text:"Value Range"}}}};case"boxplot":return{...e,scales:{y:{title:{display:!0,text:"Value"}}}};case"variability":return{...e,scales:{x:{title:{display:!0,text:"Observation"}},y:{title:{display:!0,text:"Value"}}},interaction:{mode:"index",intersect:!1}};default:return e}}}class hc{constructor(t=null){this.chartManager=t||new Fn,this.plotTypes={INDIVIDUAL:"individual",RANGE:"range",MOVING_RANGE:"movingRange",CONTROL:"control"}}createIndividualPlot(t,e,s={}){const n=e.raw,o=e.statistics,r=o.mean+3*o.standardDeviation,a=o.mean-3*o.standardDeviation,l=$n(n,s.confidence||.95),c=[{label:"Individual Values",data:n.map((d,u)=>({x:u+1,y:d})),borderColor:"#3498db",backgroundColor:"#3498db",fill:!1,tension:0,pointRadius:4,pointHoverRadius:6,pointBackgroundColor:n.map(d=>d>r||d<a?"#e74c3c":"#3498db")},{label:"Center Line (Mean)",data:Array(n.length).fill(o.mean).map((d,u)=>({x:u+1,y:d})),borderColor:"#2ecc71",backgroundColor:"#2ecc71",borderDash:[5,5],fill:!1,pointRadius:0,tension:0},{label:"Upper Control Limit (UCL)",data:Array(n.length).fill(r).map((d,u)=>({x:u+1,y:d})),borderColor:"#e74c3c",backgroundColor:"#e74c3c",borderDash:[10,5],fill:!1,pointRadius:0,tension:0},{label:"Lower Control Limit (LCL)",data:Array(n.length).fill(a).map((d,u)=>({x:u+1,y:d})),borderColor:"#e74c3c",backgroundColor:"#e74c3c",borderDash:[10,5],fill:!1,pointRadius:0,tension:0}];s.showConfidenceInterval&&(c.push({label:`${s.confidence||95}% Confidence Interval (Upper)`,data:Array(n.length).fill(l.upper).map((d,u)=>({x:u+1,y:d})),borderColor:"#95a5a6",backgroundColor:"#95a5a640",borderDash:[3,3],fill:"+1",pointRadius:0,tension:0}),c.push({label:`${s.confidence||95}% Confidence Interval (Lower)`,data:Array(n.length).fill(l.lower).map((d,u)=>({x:u+1,y:d})),borderColor:"#95a5a6",backgroundColor:"#95a5a640",borderDash:[3,3],fill:!1,pointRadius:0,tension:0}));const h={title:s.title||"Individual Values Plot (I-Chart)",xAxisLabel:s.xAxisLabel||"Observation",yAxisLabel:s.yAxisLabel||"Individual Value",showLegend:s.showLegend!==!1,showGrid:s.showGrid!==!1,chartOptions:{plugins:{tooltip:{callbacks:{title:d=>`Observation ${d[0].label}`,label:d=>{const u=d.parsed.y;let f=`${d.dataset.label}: ${u.toFixed(3)}`;return d.datasetIndex===0&&(u>r?f+=" (Above UCL)":u<a?f+=" (Below LCL)":f+=" (In Control)"),f}}},annotation:{annotations:this.createControlLimitAnnotations(r,a,o.mean)}},scales:{y:{min:Math.min(a*1.1,Math.min(...n)*.9),max:Math.max(r*1.1,Math.max(...n)*1.1)}}}};return this.chartManager.createVariabilityPlot(t,{raw:n,statistics:o,confidenceInterval:l},h)}createMovingRangePlot(t,e,s={}){const n=e.raw,o=[];for(let f=1;f<n.length;f++)o.push(Math.abs(n[f]-n[f-1]));if(o.length===0)throw new Error("Need at least 2 data points to calculate moving ranges");const r=o.reduce((f,g)=>f+g,0)/o.length,a=0,c=3.267*r,h=a*r,d=[{label:"Moving Range",data:o.map((f,g)=>({x:g+2,y:f})),borderColor:"#9b59b6",backgroundColor:"#9b59b6",fill:!1,tension:0,pointRadius:4,pointHoverRadius:6,pointBackgroundColor:o.map(f=>f>c?"#e74c3c":"#9b59b6")},{label:"Center Line (Average MR)",data:Array(o.length).fill(r).map((f,g)=>({x:g+2,y:f})),borderColor:"#2ecc71",backgroundColor:"#2ecc71",borderDash:[5,5],fill:!1,pointRadius:0,tension:0},{label:"Upper Control Limit (UCL)",data:Array(o.length).fill(c).map((f,g)=>({x:g+2,y:f})),borderColor:"#e74c3c",backgroundColor:"#e74c3c",borderDash:[10,5],fill:!1,pointRadius:0,tension:0}];h>0&&d.push({label:"Lower Control Limit (LCL)",data:Array(o.length).fill(h).map((f,g)=>({x:g+2,y:f})),borderColor:"#e74c3c",backgroundColor:"#e74c3c",borderDash:[10,5],fill:!1,pointRadius:0,tension:0});const u={title:s.title||"Moving Range Plot (MR-Chart)",xAxisLabel:s.xAxisLabel||"Observation",yAxisLabel:s.yAxisLabel||"Moving Range",showLegend:s.showLegend!==!1,showGrid:s.showGrid!==!1,chartOptions:{plugins:{tooltip:{callbacks:{title:f=>`Observations ${f[0].label-1}-${f[0].label}`,label:f=>{const g=f.parsed.y;let p=`${f.dataset.label}: ${g.toFixed(3)}`;return f.datasetIndex===0&&(g>c?p+=" (Above UCL)":p+=" (In Control)"),p}}}},scales:{x:{min:1.5,max:n.length+.5},y:{min:0,max:Math.max(c*1.1,Math.max(...o)*1.1)}}}};return this.chartManager.createVariabilityPlot(t,{raw:o,statistics:{mean:r},metadata:{type:"movingRange",originalDataLength:n.length}},u)}createControlLimitAnnotations(t,e,s){return{ucl:{type:"line",yMin:t,yMax:t,borderColor:"#e74c3c",borderWidth:2,borderDash:[10,5],label:{content:"UCL",enabled:!0,position:"end"}},lcl:{type:"line",yMin:e,yMax:e,borderColor:"#e74c3c",borderWidth:2,borderDash:[10,5],label:{content:"LCL",enabled:!0,position:"end"}},centerLine:{type:"line",yMin:s,yMax:s,borderColor:"#2ecc71",borderWidth:2,borderDash:[5,5],label:{content:"CL",enabled:!0,position:"end"}}}}analyzeControlPatterns(t,e,s,n){const o={outOfControl:[],trends:[],runs:[],cycles:[]};t.forEach((l,c)=>{(l>e||l<s)&&o.outOfControl.push({index:c+1,value:l,rule:"Beyond control limits"})});let r=0,a=0;t.forEach((l,c)=>{l>n?(r++,a=0,r>=7&&o.runs.push({startIndex:c-6,endIndex:c+1,rule:"7 consecutive points above center line"})):l<n?(a++,r=0,a>=7&&o.runs.push({startIndex:c-6,endIndex:c+1,rule:"7 consecutive points below center line"})):(r=0,a=0)});for(let l=0;l<=t.length-6;l++){let c=!0,h=!0;for(let d=l;d<l+5;d++)t[d]>=t[d+1]&&(c=!1),t[d]<=t[d+1]&&(h=!1);c&&o.trends.push({startIndex:l+1,endIndex:l+6,rule:"6 consecutive increasing points"}),h&&o.trends.push({startIndex:l+1,endIndex:l+6,rule:"6 consecutive decreasing points"})}return o}}class dc{constructor(){this.activeTab="analysis",this.isFullscreen=!1}init(){this.setupResponsiveHandlers(),this.setupKeyboardShortcuts(),this.setupTooltips(),this.initializeFormValidation()}setupResponsiveHandlers(){window.addEventListener("resize",()=>{this.handleResize()}),window.addEventListener("orientationchange",()=>{setTimeout(()=>this.handleResize(),100)})}handleResize(){const t=new CustomEvent("chartResize");document.dispatchEvent(t)}setupKeyboardShortcuts(){document.addEventListener("keydown",t=>{if((t.ctrlKey||t.metaKey)&&t.key==="Enter"){t.preventDefault();const e=document.getElementById("generate-plot");e&&!e.disabled&&e.click()}if(t.key==="Escape"&&this.isFullscreen&&document.exitFullscreen(),t.ctrlKey&&t.key>="1"&&t.key<="3"){t.preventDefault();const e=["data","analysis","export"],s=parseInt(t.key)-1;e[s]&&this.switchToTab(e[s])}})}switchToTab(t){const e=document.querySelector(`[data-tab="${t}"]`);e&&e.click()}setupTooltips(){[{selector:"#show-mean",text:"Display the mean line on the chart"},{selector:"#show-confidence",text:"Show confidence interval bands"},{selector:"#show-grid",text:"Display grid lines for easier reading"},{selector:"#show-legend",text:"Show chart legend"},{selector:"#export-chart",text:"Export chart as PNG image"},{selector:"#fullscreen-chart",text:"View chart in fullscreen mode"}].forEach(({selector:e,text:s})=>{const n=document.querySelector(e);n&&(n.title=s)})}initializeFormValidation(){const t=document.getElementById("manual-data");t&&t.addEventListener("input",n=>{this.validateDataInput(n.target)});const e=document.getElementById("group-data");e&&e.addEventListener("input",n=>{this.validateGroupInput(n.target)});const s=document.getElementById("chart-title");s&&s.addEventListener("input",n=>{this.validateChartTitle(n.target)})}validateDataInput(t){const e=t.value.trim(),s=this.isValidDataInput(e);this.setInputValidation(t,s,s?"":"Please enter numbers separated by spaces or commas")}validateGroupInput(t){const e=t.value.trim(),s=document.getElementById("manual-data").value.trim();if(!e){this.setInputValidation(t,!0,"");return}if(!s){this.setInputValidation(t,!1,"Please enter data first");return}try{const n=s.split(/[,;\s\t]+/).filter(a=>a.trim()).length,o=e.split(/[,;\s\t]+/).filter(a=>a.trim()).length,r=n===o;this.setInputValidation(t,r,r?"":`Number of groups (${o}) must match data points (${n})`)}catch{this.setInputValidation(t,!1,"Invalid group format")}}validateChartTitle(t){const s=t.value.trim().length<=100;this.setInputValidation(t,s,s?"":"Chart title must be 100 characters or less")}isValidDataInput(t){if(!t)return!1;try{const e=t.split(/[,;\s\t]+/).filter(s=>s.trim()).map(s=>parseFloat(s.trim()));return e.length>0&&e.every(s=>!isNaN(s))}catch{return!1}}setInputValidation(t,e,s){t.classList.remove("valid","invalid"),t.value.trim()&&t.classList.add(e?"valid":"invalid");let n=t.parentNode.querySelector(".validation-message");s?(n||(n=document.createElement("div"),n.className="validation-message",t.parentNode.appendChild(n)),n.textContent=s,n.className=`validation-message ${e?"valid":"invalid"}`):n&&n.remove()}setElementLoading(t,e){const s=document.getElementById(t);s&&(e?(s.disabled=!0,s.classList.add("loading"),s.dataset.originalText||(s.dataset.originalText=s.textContent),s.textContent="Loading..."):(s.disabled=!1,s.classList.remove("loading"),s.dataset.originalText&&(s.textContent=s.dataset.originalText,delete s.dataset.originalText)))}updateProgress(t,e=""){let s=document.getElementById("progress-bar");s||(s=this.createProgressBar());const n=s.querySelector(".progress-fill"),o=s.querySelector(".progress-text");n.style.width=`${Math.max(0,Math.min(100,t))}%`,o.textContent=e,s.classList.toggle("hidden",t<=0||t>=100)}createProgressBar(){const t=document.createElement("div");return t.id="progress-bar",t.className="progress-bar hidden",t.innerHTML=`
      <div class="progress-track">
        <div class="progress-fill"></div>
      </div>
      <div class="progress-text"></div>
    `,document.body.appendChild(t),t}animateIn(t,e="fadeIn"){t.classList.add("animate",e),t.addEventListener("animationend",()=>{t.classList.remove("animate",e)},{once:!0})}animateOut(t,e="fadeOut"){return new Promise(s=>{t.classList.add("animate",e),t.addEventListener("animationend",()=>{t.classList.remove("animate",e),s()},{once:!0})})}scrollToElement(t,e=0){const s=document.getElementById(t);if(!s)return;const n=s.offsetTop-e;window.scrollTo({top:n,behavior:"smooth"})}async copyToClipboard(t){try{return await navigator.clipboard.writeText(t),!0}catch{const s=document.createElement("textarea");s.value=t,s.style.position="fixed",s.style.opacity="0",document.body.appendChild(s),s.select();try{return document.execCommand("copy"),!0}catch{return!1}finally{document.body.removeChild(s)}}}formatNumber(t,e=3){return typeof t!="number"||isNaN(t)?"N/A":t.toFixed(e)}debounce(t,e){let s;return function(...o){const r=()=>{clearTimeout(s),t(...o)};clearTimeout(s),s=setTimeout(r,e)}}throttle(t,e){let s;return function(...o){s||(t.apply(this,o),s=!0,setTimeout(()=>s=!1,e))}}}class uc{constructor(){this.container=null,this.notifications=new Map,this.defaultDuration=5e3,this.maxNotifications=5,this.init()}init(){this.container=document.getElementById("notifications"),this.container||this.createContainer()}createContainer(){this.container=document.createElement("div"),this.container.id="notifications",this.container.className="notifications",document.body.appendChild(this.container)}show(t,e="info",s=this.defaultDuration,n={}){const o=this.generateId(),r=this.createNotification(o,t,e,n);return this.container.appendChild(r),this.notifications.set(o,{element:r,type:e,message:t}),requestAnimationFrame(()=>{r.classList.add("show")}),s>0&&setTimeout(()=>{this.remove(o)},s),this.enforceMaxNotifications(),o}success(t,e=this.defaultDuration){return this.show(t,"success",e)}error(t,e=0){return this.show(t,"error",e)}warning(t,e=this.defaultDuration){return this.show(t,"warning",e)}info(t,e=this.defaultDuration){return this.show(t,"info",e)}createNotification(t,e,s,n={}){const o=document.createElement("div");o.id=`notification-${t}`,o.className=`notification ${s}`,o.setAttribute("role","alert"),o.setAttribute("aria-live","polite");const r=document.createElement("div");r.className="notification-content";const a=document.createElement("span");a.className="notification-icon",a.innerHTML=this.getIcon(s),r.appendChild(a);const l=document.createElement("div");if(l.className="notification-message",l.textContent=e,r.appendChild(l),n.closable!==!1){const c=document.createElement("button");c.className="notification-close",c.innerHTML="×",c.setAttribute("aria-label","Close notification"),c.addEventListener("click",()=>{this.remove(t)}),r.appendChild(c)}if(n.actions&&Array.isArray(n.actions)){const c=document.createElement("div");c.className="notification-actions",n.actions.forEach(h=>{const d=document.createElement("button");d.className=`notification-action ${h.type||"secondary"}`,d.textContent=h.label,d.addEventListener("click",()=>{h.handler&&h.handler(),h.dismiss!==!1&&this.remove(t)}),c.appendChild(d)}),r.appendChild(c)}if(o.appendChild(r),n.showProgress&&n.duration>0){const c=document.createElement("div");c.className="notification-progress",c.style.animationDuration=`${n.duration}ms`,o.appendChild(c)}return o}remove(t){const e=this.notifications.get(t);if(!e)return;const{element:s}=e;s.classList.add("removing"),setTimeout(()=>{s.parentNode&&s.parentNode.removeChild(s),this.notifications.delete(t)},300)}clear(){this.notifications.forEach((t,e)=>{this.remove(e)})}clearType(t){this.notifications.forEach((e,s)=>{e.type===t&&this.remove(s)})}update(t,e,s=null){const n=this.notifications.get(t);if(!n)return;const{element:o}=n,r=o.querySelector(".notification-message");if(r&&(r.textContent=e,n.message=e),s&&s!==n.type){o.className=`notification ${s}`;const a=o.querySelector(".notification-icon");a&&(a.innerHTML=this.getIcon(s)),n.type=s}}getIcon(t){const e={success:"✓",error:"✕",warning:"⚠",info:"ℹ"};return e[t]||e.info}generateId(){return`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}enforceMaxNotifications(){if(this.notifications.size<=this.maxNotifications)return;Array.from(this.notifications.keys()).slice(0,this.notifications.size-this.maxNotifications).forEach(s=>{this.remove(s)})}confirm(t,e={}){return new Promise(s=>{this.show(t,"warning",0,{closable:!1,actions:[{label:e.confirmLabel||"Confirm",type:"primary",handler:()=>s(!0)},{label:e.cancelLabel||"Cancel",type:"secondary",handler:()=>s(!1)}]})})}loading(t="Loading..."){return this.show(t,"info",0,{closable:!1,showProgress:!1})}updateLoading(t,e){this.update(t,e)}completeLoading(t,e,s="success"){this.update(t,e,s),setTimeout(()=>{this.remove(t)},2e3)}progress(t,e=0){const s=this.generateId(),n=this.createProgressNotification(s,t,e);return this.container.appendChild(n),this.notifications.set(s,{element:n,type:"progress",message:t}),requestAnimationFrame(()=>{n.classList.add("show")}),s}updateProgress(t,e,s){const n=this.notifications.get(t);if(!n)return;const{element:o}=n,r=o.querySelector(".notification-message"),a=o.querySelector(".progress-fill"),l=o.querySelector(".progress-text");r&&(r.textContent=e),a&&(a.style.width=`${Math.max(0,Math.min(100,s))}%`),l&&(l.textContent=`${Math.round(s)}%`)}createProgressNotification(t,e,s){const n=document.createElement("div");return n.id=`notification-${t}`,n.className="notification progress",n.innerHTML=`
      <div class="notification-content">
        <div class="notification-message">${e}</div>
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${s}%"></div>
          </div>
          <div class="progress-text">${Math.round(s)}%</div>
        </div>
      </div>
    `,n}getCount(t=null){if(!t)return this.notifications.size;let e=0;return this.notifications.forEach(s=>{s.type===t&&e++}),e}exists(t){return this.notifications.has(t)}}class fc{constructor(){this.supportedFormats={csv:"text/csv",json:"application/json",txt:"text/plain",png:"image/png",jpeg:"image/jpeg",jpg:"image/jpeg"}}readFileAsText(t){return new Promise((e,s)=>{if(!t){s(new Error("No file provided"));return}const n=new FileReader;n.onload=o=>{e(o.target.result)},n.onerror=()=>{s(new Error("Failed to read file"))},n.readAsText(t)})}readFileAsDataURL(t){return new Promise((e,s)=>{if(!t){s(new Error("No file provided"));return}const n=new FileReader;n.onload=o=>{e(o.target.result)},n.onerror=()=>{s(new Error("Failed to read file"))},n.readAsDataURL(t)})}downloadFile(t,e,s="text/plain"){try{const n=new Blob([t],{type:s}),o=URL.createObjectURL(n),r=document.createElement("a");r.href=o,r.download=e,r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r),setTimeout(()=>{URL.revokeObjectURL(o)},100)}catch(n){throw new Error(`Failed to download file: ${n.message}`)}}downloadImageFromDataURL(t,e){try{const s=document.createElement("a");s.href=t,s.download=e,s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)}catch(s){throw new Error(`Failed to download image: ${s.message}`)}}downloadBlob(t,e){try{const s=URL.createObjectURL(t),n=document.createElement("a");n.href=s,n.download=e,n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n),setTimeout(()=>{URL.revokeObjectURL(s)},100)}catch(s){throw new Error(`Failed to download blob: ${s.message}`)}}validateFileType(t,e){return t?e.includes(t.type):!1}validateFileSize(t,e){return t?t.size<=e:!1}getFileExtension(t){if(!t)return"";const e=t.lastIndexOf(".");return e>0?t.substring(e+1).toLowerCase():""}getMimeType(t){return this.supportedFormats[t.toLowerCase()]||"application/octet-stream"}formatFileSize(t){if(t===0)return"0 Bytes";const e=1024,s=["Bytes","KB","MB","GB"],n=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,n)).toFixed(2))+" "+s[n]}createCSV(t,e){const s=[];return t&&t.length>0&&s.push(t.map(n=>this.escapeCSVField(n)).join(",")),e.forEach(n=>{const o=n.map(r=>this.escapeCSVField(r)).join(",");s.push(o)}),s.join(`
`)}escapeCSVField(t){if(t==null)return"";const e=String(t);return e.includes(",")||e.includes(`
`)||e.includes('"')?'"'+e.replace(/"/g,'""')+'"':e}parseCSV(t){if(!t||t.trim()==="")throw new Error("CSV content is empty");const e=t.trim().split(`
`);if(e.length===0||e.length===1&&e[0].trim()==="")throw new Error("CSV content is empty");const s={headers:[],rows:[]};s.headers=this.parseCSVLine(e[0]);for(let n=1;n<e.length;n++){const o=this.parseCSVLine(e[n]);o.length>0&&s.rows.push(o)}return s}parseCSVLine(t){const e=[];let s="",n=!1,o=0;for(;o<t.length;){const r=t[o],a=t[o+1];r==='"'?n&&a==='"'?(s+='"',o+=2):(n=!n,o++):r===","&&!n?(e.push(s.trim()),s="",o++):(s+=r,o++)}return e.push(s.trim()),e}createJSON(t,e=!0){try{return e?JSON.stringify(t,null,2):JSON.stringify(t)}catch(s){throw new Error(`Failed to create JSON: ${s.message}`)}}createTextReport(t,e={}){const{title:s="Data Analysis Report",includeTimestamp:n=!0,includeStatistics:o=!0,includeRawData:r=!1}=e;let a="";return a+=s+`
`,a+="=".repeat(s.length)+`

`,n&&(a+=`Generated: ${new Date().toLocaleString()}

`),o&&t.statistics&&(a+=`STATISTICAL SUMMARY
`,a+=`-------------------
`,Object.entries(t.statistics).forEach(([l,c])=>{const h=l.replace(/([A-Z])/g," $1").replace(/^./,u=>u.toUpperCase()),d=typeof c=="number"?c.toFixed(4):c;a+=`${h}: ${d}
`}),a+=`
`),r&&t.raw&&(a+=`RAW DATA
`,a+=`--------
`,a+=t.raw.join(", ")+`

`),t.groups&&t.groups.length>0&&(a+=`GROUPS
`,a+=`------
`,a+=t.groups.join(", ")+`

`),t.metadata&&(a+=`METADATA
`,a+=`--------
`,Object.entries(t.metadata).forEach(([l,c])=>{const h=l.replace(/([A-Z])/g," $1").replace(/^./,d=>d.toUpperCase());a+=`${h}: ${c}
`})),a}supportsDownload(){return typeof document<"u"&&typeof document.createElement=="function"&&typeof URL<"u"&&typeof URL.createObjectURL=="function"}supportsFileReading(){return typeof FileReader<"u"}getFileInfo(t){return t?{name:t.name,size:t.size,type:t.type,lastModified:t.lastModified,extension:this.getFileExtension(t.name),formattedSize:this.formatFileSize(t.size),lastModifiedDate:new Date(t.lastModified)}:null}}class gc{constructor(){this.random=Math.random}generateNormalDistribution(t=50,e=100,s=15){const n=[];for(let o=0;o<t;o+=2){const r=this.random(),a=this.random(),l=Math.sqrt(-2*Math.log(r))*Math.cos(2*Math.PI*a),c=Math.sqrt(-2*Math.log(r))*Math.sin(2*Math.PI*a);n.push(l*s+e),n.length<t&&n.push(c*s+e)}return n.slice(0,t)}generateSkewedData(t=40,e=.5){const s=[];for(let n=0;n<t;n++){const o=this.random(),r=-Math.log(1-o)/e;s.push(r)}return s}generateGroupedData(t={}){const{groups:e=["A","B","C"],pointsPerGroup:s=15,baseMean:n=50,meanDifference:o=10,stdDev:r=5}=t,a=[],l=[];return e.forEach((c,h)=>{const d=n+h*o,u=this.generateNormalDistribution(s,d,r);a.push(...u),l.push(...Array(s).fill(c))}),{data:a,groups:l}}generateDataWithOutliers(t=30,e=10){const s=Math.floor(t*(100-e)/100),n=t-s,o=this.generateNormalDistribution(s,50,8),r=[];for(let l=0;l<n;l++){const h=this.random()>.5?80+this.random()*20:20-this.random()*20;r.push(h)}const a=[...o,...r];return this.shuffleArray(a)}generateTimeSeriesData(t=50,e={}){const{startValue:s=100,trend:n=.5,seasonality:o=5,noise:r=2,seasonalPeriod:a=12}=e,l=[],c=[],h=new Date;for(let d=0;d<t;d++){const u=s+n*d,f=o*Math.sin(2*Math.PI*d/a),g=this.generateNormalDistribution(1,0,r)[0],p=u+f+g;l.push(p);const m=new Date(h);m.setDate(h.getDate()+d),c.push(m)}return{data:l,timestamps:c,trend:{slope:n,intercept:s,seasonalAmplitude:o,period:a}}}generateBimodalData(t=60,e={}){const{mean1:s=30,mean2:n=70,stdDev1:o=8,stdDev2:r=8,proportion1:a=.5}=e,l=[],c=Math.floor(t*a),h=t-c,d=this.generateNormalDistribution(c,s,o),u=this.generateNormalDistribution(h,n,r);return l.push(...d,...u),this.shuffleArray(l)}generateUniformData(t=40,e=0,s=100){const n=[];for(let o=0;o<t;o++){const r=e+this.random()*(s-e);n.push(r)}return n}generateControlChartData(t=100,e={}){const{targetMean:s=50,processStdDev:n=2,shiftPoint:o=null,shiftMagnitude:r=5,trendStart:a=null,trendRate:l=.1}=e,c=[],h=[];for(let d=0;d<t;d++){let u=s;o!==null&&d>=o&&(u+=r,d===o&&h.push({point:d,type:"shift",magnitude:r})),a!==null&&d>=a&&(u+=(d-a)*l,d===a&&h.push({point:d,type:"trend",rate:l}));const f=this.generateNormalDistribution(1,u,n)[0];c.push(f)}return{data:c,specialCauses:h,controlLimits:{ucl:s+3*n,lcl:s-3*n,centerLine:s}}}generateMSAData(t=10,e=3,s=2){const n=[],o=Array.from({length:t},(h,d)=>`Part_${d+1}`),r=Array.from({length:e},(h,d)=>`Operator_${String.fromCharCode(65+d)}`),a=this.generateNormalDistribution(t,50,10),l=this.generateNormalDistribution(e,0,1),c=.5;return o.forEach((h,d)=>{r.forEach((u,f)=>{for(let g=1;g<=s;g++){const p=a[d],m=l[f],b=this.generateNormalDistribution(1,0,c)[0],y=p+m+b;n.push({part:h,operator:u,trial:g,measurement:y,trueValue:p})}})}),{data:n,partNames:o,operatorNames:r,truePartValues:a,operatorBias:l,measurementError:c}}shuffleArray(t){const e=[...t];for(let s=e.length-1;s>0;s--){const n=Math.floor(this.random()*(s+1));[e[s],e[n]]=[e[n],e[s]]}return e}getSampleDataset(t,e={}){const n={quality_measurements:()=>({data:this.generateNormalDistribution(50,100,5),description:"Quality measurements from a manufacturing process",units:"mm"}),customer_satisfaction:()=>({data:this.generateSkewedData(100,.3).map(o=>Math.min(10,Math.max(1,Math.round(o+1)))),description:"Customer satisfaction ratings (1-10 scale)",units:"rating"}),sales_by_region:()=>{const o=this.generateGroupedData({groups:["North","South","East","West"],pointsPerGroup:12,baseMean:1e3,meanDifference:200,stdDev:150});return{data:o.data,groups:o.groups,description:"Monthly sales data by region",units:"thousands USD"}},process_control:()=>{const o=this.generateControlChartData(100,{shiftPoint:60,shiftMagnitude:3});return{data:o.data,description:"Process control measurements with a shift at point 60",units:"units",specialCauses:o.specialCauses}},temperature_readings:()=>{const o=this.generateTimeSeriesData(365,{startValue:20,trend:.01,seasonality:15,seasonalPeriod:91.25,noise:3});return{data:o.data,timestamps:o.timestamps,description:"Daily temperature readings over one year",units:"°C"}}}[t];if(!n)throw new Error(`Unknown sample dataset: ${t}`);return n()}getAvailableDatasets(){return[{name:"quality_measurements",description:"Quality measurements from manufacturing"},{name:"customer_satisfaction",description:"Customer satisfaction ratings"},{name:"sales_by_region",description:"Sales data grouped by region"},{name:"process_control",description:"Process control data with special causes"},{name:"temperature_readings",description:"Time series temperature data"}]}}class pc{constructor(){this.dataProcessor=new jn,this.chartManager=new Fn,this.variabilityPlot=new hc(this.chartManager),this.uiManager=new dc,this.notifications=new uc,this.fileManager=new fc,this.sampleData=new gc,this.currentData=null,this.currentChart=null,this.init()}init(){this.setupEventListeners(),this.uiManager.init(),this.showNotification("Application loaded successfully!","success")}setupEventListeners(){document.querySelectorAll(".nav-btn").forEach(t=>{t.addEventListener("click",e=>{this.switchTab(e.target.dataset.tab)})}),document.getElementById("load-manual-data").addEventListener("click",()=>{this.loadManualData()}),document.getElementById("csv-file").addEventListener("change",t=>{this.loadCSVFile(t.target.files[0])}),document.querySelectorAll("[data-sample]").forEach(t=>{t.addEventListener("click",e=>{this.loadSampleData(e.target.dataset.sample)})}),document.getElementById("generate-plot").addEventListener("click",()=>{this.generatePlot()}),document.getElementById("export-chart").addEventListener("click",()=>{this.exportCurrentChart()}),document.getElementById("fullscreen-chart").addEventListener("click",()=>{this.toggleFullscreen()}),document.getElementById("export-csv").addEventListener("click",()=>{this.exportData("csv")}),document.getElementById("export-json").addEventListener("click",()=>{this.exportData("json")}),document.getElementById("export-txt").addEventListener("click",()=>{this.exportData("txt")}),document.getElementById("export-png").addEventListener("click",()=>{this.exportChart("png")}),document.getElementById("export-jpeg").addEventListener("click",()=>{this.exportChart("jpeg")}),document.getElementById("export-report").addEventListener("click",()=>{this.exportReport()}),document.getElementById("chart-title").addEventListener("input",t=>{document.getElementById("chart-title-display").textContent=t.target.value||"Chart"})}switchTab(t){document.querySelectorAll(".nav-btn").forEach(e=>{e.classList.toggle("active",e.dataset.tab===t)}),document.querySelectorAll(".tab-content").forEach(e=>{e.classList.toggle("active",e.id===`${t}-tab`)})}async loadManualData(){try{this.showLoading(!0);const t=document.getElementById("manual-data").value.trim(),e=document.getElementById("group-data").value.trim();if(!t)throw new Error("Please enter some data");const s=this.dataProcessor.parseStringData(t);let n=[];if(e&&(n=e.split(/[,;\s\t]+/).filter(o=>o.trim()!==""),n.length!==s.length))throw new Error(`Number of groups (${n.length}) must match number of data points (${s.length})`);this.dataProcessor.setData(s,n),this.currentData=this.dataProcessor.getPlotData(),this.updateDataSummary(),this.updateStatistics(),this.showNotification(`Loaded ${s.length} data points successfully!`,"success"),this.switchTab("analysis")}catch(t){this.showNotification(t.message,"error")}finally{this.showLoading(!1)}}async loadCSVFile(t){if(t)try{this.showLoading(!0);const e=await this.fileManager.readFileAsText(t),s=this.dataProcessor.parseCSVData(e);this.showCSVPreview(s);const n=s.rows.map(r=>parseFloat(r[0])).filter(r=>!isNaN(r)),o=s.headers.length>1?s.rows.map(r=>r[1]):[];if(n.length===0)throw new Error("No valid numerical data found in CSV");this.dataProcessor.setData(n,o),this.currentData=this.dataProcessor.getPlotData(),this.updateDataSummary(),this.updateStatistics(),this.showNotification(`Loaded ${n.length} data points from CSV!`,"success")}catch(e){this.showNotification(e.message,"error")}finally{this.showLoading(!1)}}loadSampleData(t){try{let e,s;switch(t){case"normal":e=this.sampleData.generateNormalDistribution(50,100,15);break;case"skewed":e=this.sampleData.generateSkewedData(40);break;case"grouped":const n=this.sampleData.generateGroupedData();e=n.data,s=n.groups;break;case"outliers":e=this.sampleData.generateDataWithOutliers(30);break;default:throw new Error("Unknown sample data type")}this.dataProcessor.setData(e,s||[]),this.currentData=this.dataProcessor.getPlotData(),this.updateDataSummary(),this.updateStatistics(),this.showNotification(`Loaded ${t} sample data (${e.length} points)!`,"success"),this.switchTab("analysis")}catch(e){this.showNotification(e.message,"error")}}async generatePlot(){if(!this.currentData){this.showNotification("Please load data first","warning");return}try{this.showLoading(!0);const t=document.getElementById("plot-type").value,e=parseFloat(document.getElementById("confidence-level").value),n={title:document.getElementById("chart-title").value||"Chart",confidence:e,showMean:document.getElementById("show-mean").checked,showConfidenceInterval:document.getElementById("show-confidence").checked,showGrid:document.getElementById("show-grid").checked,showLegend:document.getElementById("show-legend").checked};switch(this.currentChart&&this.chartManager.destroyChart("main-chart"),document.getElementById("chart-placeholder").classList.add("hidden"),document.getElementById("main-chart").style.display="block",t){case"histogram":this.currentChart=this.chartManager.createHistogram("main-chart",this.currentData.histogram,n);break;case"boxplot":this.currentChart=this.chartManager.createBoxPlot("main-chart",this.currentData.boxplot,n);break;case"variability":this.currentChart=this.chartManager.createVariabilityPlot("main-chart",this.currentData,n);break;case"individual":this.currentChart=this.variabilityPlot.createIndividualPlot("main-chart",this.currentData,n);break;case"movingRange":this.currentChart=this.variabilityPlot.createMovingRangePlot("main-chart",this.currentData,n);break;case"scatter":this.currentChart=this.chartManager.createScatterPlot("main-chart",this.currentData.groupedStats,n);break;default:throw new Error("Unknown plot type")}this.showNotification(`${t} chart generated successfully!`,"success")}catch(t){this.showNotification(t.message,"error")}finally{this.showLoading(!1)}}updateDataSummary(){if(!this.currentData)return;const t=this.currentData.statistics,e=document.getElementById("data-summary");document.getElementById("summary-count").textContent=t.count,document.getElementById("summary-mean").textContent=t.mean.toFixed(3),document.getElementById("summary-median").textContent=t.median.toFixed(3),document.getElementById("summary-std").textContent=t.standardDeviation.toFixed(3),document.getElementById("summary-min").textContent=t.min.toFixed(3),document.getElementById("summary-max").textContent=t.max.toFixed(3),e.classList.remove("hidden")}updateStatistics(){if(!this.currentData)return;const t=this.currentData.statistics,e=document.getElementById("statistics-content");e.innerHTML=`
      <div class="summary-grid">
        <div class="summary-item">
          <span class="summary-label">Count:</span>
          <span class="summary-value">${t.count}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Mean:</span>
          <span class="summary-value">${t.mean.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Median:</span>
          <span class="summary-value">${t.median.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Std Dev:</span>
          <span class="summary-value">${t.standardDeviation.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Variance:</span>
          <span class="summary-value">${t.variance.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Min:</span>
          <span class="summary-value">${t.min.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Max:</span>
          <span class="summary-value">${t.max.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Range:</span>
          <span class="summary-value">${t.range.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Q1:</span>
          <span class="summary-value">${t.q1.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Q3:</span>
          <span class="summary-value">${t.q3.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">IQR:</span>
          <span class="summary-value">${t.iqr.toFixed(4)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Skewness:</span>
          <span class="summary-value">${t.skewness.toFixed(4)}</span>
        </div>
      </div>
    `}showCSVPreview(t){const e=document.getElementById("csv-preview"),s=5;let n='<h4>CSV Preview:</h4><table class="csv-table">';n+="<thead><tr>",t.headers.forEach(o=>{n+=`<th>${o}</th>`}),n+="</tr></thead><tbody>",t.rows.slice(0,s).forEach(o=>{n+="<tr>",o.forEach(r=>{n+=`<td>${r}</td>`}),n+="</tr>"}),t.rows.length>s&&(n+=`<tr><td colspan="${t.headers.length}">... and ${t.rows.length-s} more rows</td></tr>`),n+="</tbody></table>",e.innerHTML=n,e.classList.remove("hidden")}exportData(t){if(!this.currentData){this.showNotification("No data to export","warning");return}try{const e=this.dataProcessor.exportData(t);this.fileManager.downloadFile(e,`data.${t}`,this.getContentType(t)),this.showNotification(`Data exported as ${t.toUpperCase()}!`,"success")}catch(e){this.showNotification(e.message,"error")}}exportChart(t){if(!this.currentChart){this.showNotification("No chart to export","warning");return}try{const e=this.chartManager.exportChart("main-chart",t);this.fileManager.downloadImageFromDataURL(e,`chart.${t}`),this.showNotification(`Chart exported as ${t.toUpperCase()}!`,"success")}catch(e){this.showNotification(e.message,"error")}}exportCurrentChart(){this.exportChart("png")}toggleFullscreen(){const t=document.querySelector(".chart-card");document.fullscreenElement?document.exitFullscreen():t.requestFullscreen()}exportReport(){if(!this.currentData){this.showNotification("No data to include in report","warning");return}try{const t=document.getElementById("report-title").value||"Statistical Analysis Report",e=document.getElementById("include-data").checked,s=document.getElementById("include-statistics").checked,n=document.getElementById("include-chart").checked;let o=`${t}
${"=".repeat(t.length)}

`;if(o+=`Generated on: ${new Date().toLocaleString()}

`,s){o+=`STATISTICAL SUMMARY
`,o+=`-------------------
`;const r=this.currentData.statistics;Object.entries(r).forEach(([a,l])=>{const c=typeof l=="number"?l.toFixed(4):l;o+=`${a}: ${c}
`}),o+=`
`}e&&(o+=`RAW DATA
`,o+=`--------
`,o+=this.currentData.raw.join(", ")+`

`,this.currentData.groups.length>0&&(o+=`GROUPS
`,o+=`------
`,o+=this.currentData.groups.join(", ")+`

`)),this.fileManager.downloadFile(o,`${t.replace(/\s+/g,"_")}.txt`,"text/plain"),this.showNotification("Report exported successfully!","success")}catch(t){this.showNotification(t.message,"error")}}showLoading(t){document.getElementById("loading-overlay").classList.toggle("hidden",!t)}showNotification(t,e="info"){this.notifications.show(t,e)}getContentType(t){return{csv:"text/csv",json:"application/json",txt:"text/plain"}[t]||"text/plain"}}document.addEventListener("DOMContentLoaded",()=>{new pc});
