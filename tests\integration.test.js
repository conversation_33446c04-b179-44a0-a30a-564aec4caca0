import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DataProcessor } from '../src/data/dataProcessor.js';
import { ChartManager } from '../src/charts/chartManager.js';
import { VariabilityPlot } from '../src/charts/variabilityPlot.js';
import { NotificationManager } from '../src/utils/notifications.js';
import { FileManager } from '../src/utils/fileManager.js';
import { SampleDataGenerator } from '../src/utils/sampleData.js';

// Mock Chart.js
vi.mock('chart.js', () => {
  const mockChart = vi.fn().mockImplementation((ctx, config) => ({
    ctx,
    config,
    data: config.data,
    options: config.options,
    destroy: vi.fn(),
    update: vi.fn(),
    resize: vi.fn(),
    toBase64Image: vi.fn(() => 'data:image/png;base64,mock-image-data'),
    chartArea: { left: 50, right: 350, top: 50, bottom: 250 },
    scales: {
      y: {
        getPixelForValue: vi.fn((value) => 200 - value * 10)
      }
    }
  }));

  mockChart.register = vi.fn();

  return {
    Chart: mockChart,
    CategoryScale: vi.fn(),
    LinearScale: vi.fn(),
    BarElement: vi.fn(),
    LineElement: vi.fn(),
    PointElement: vi.fn(),
    Title: vi.fn(),
    Tooltip: vi.fn(),
    Legend: vi.fn(),
    Filler: vi.fn()
  };
});

describe('Integration Tests', () => {
  let dataProcessor;
  let chartManager;
  let variabilityPlot;
  let notifications;
  let fileManager;
  let sampleData;

  beforeEach(() => {
    dataProcessor = new DataProcessor();
    chartManager = new ChartManager();
    variabilityPlot = new VariabilityPlot(chartManager);
    notifications = new NotificationManager();
    fileManager = new FileManager();
    sampleData = new SampleDataGenerator();

    // Mock DOM elements
    global.document = {
      getElementById: vi.fn((id) => {
        if (id === 'notifications') {
          return {
            id,
            appendChild: vi.fn(),
            removeChild: vi.fn(),
            classList: {
              add: vi.fn(),
              remove: vi.fn(),
              toggle: vi.fn(),
              contains: vi.fn()
            }
          };
        }
        return {
          id,
          getContext: vi.fn(() => ({
            save: vi.fn(),
            restore: vi.fn(),
            fillRect: vi.fn(),
            strokeRect: vi.fn(),
            beginPath: vi.fn(),
            moveTo: vi.fn(),
            lineTo: vi.fn(),
            stroke: vi.fn()
          }))
        };
      }),
      createElement: vi.fn(() => ({
        id: '',
        className: '',
        innerHTML: '',
        textContent: '',
        style: {},
        appendChild: vi.fn(),
        removeChild: vi.fn(),
        addEventListener: vi.fn(),
        setAttribute: vi.fn(),
        parentNode: null,
        classList: {
          add: vi.fn(),
          remove: vi.fn(),
          toggle: vi.fn(),
          contains: vi.fn()
        }
      })),
      body: {
        appendChild: vi.fn(),
        removeChild: vi.fn()
      }
    };
  });

  afterEach(() => {
    chartManager.destroyAllCharts();
    notifications.clear();
  });

  describe('Complete Data Analysis Workflow', () => {
    it('should process data from input to chart generation', () => {
      // Step 1: Generate sample data
      const rawData = sampleData.generateNormalDistribution(30, 50, 10);
      expect(rawData).toHaveLength(30);
      expect(rawData.every(val => typeof val === 'number')).toBe(true);

      // Step 2: Process data
      dataProcessor.setData(rawData);
      const plotData = dataProcessor.getPlotData();
      
      expect(plotData.raw).toEqual(rawData);
      expect(plotData.statistics).toBeDefined();
      expect(plotData.histogram).toBeDefined();
      expect(plotData.boxplot).toBeDefined();

      // Step 3: Generate charts
      const histogram = chartManager.createHistogram('test-canvas', plotData.histogram);
      expect(histogram).toBeDefined();
      expect(histogram.config.type).toBe('bar');

      const boxplot = chartManager.createBoxPlot('test-canvas-2', plotData.boxplot);
      expect(boxplot).toBeDefined();
      expect(boxplot.config.type).toBe('scatter');

      const variability = chartManager.createVariabilityPlot('test-canvas-3', plotData);
      expect(variability).toBeDefined();
      expect(variability.config.type).toBe('line');
    });

    it('should handle grouped data workflow', () => {
      // Generate grouped data
      const groupedSample = sampleData.generateGroupedData({
        groups: ['A', 'B', 'C'],
        pointsPerGroup: 10
      });

      // Process grouped data
      dataProcessor.setData(groupedSample.data, groupedSample.groups);
      const plotData = dataProcessor.getPlotData();

      expect(plotData.groups).toEqual(groupedSample.groups);
      expect(Object.keys(plotData.groupedStats)).toEqual(['A', 'B', 'C']);

      // Create scatter plot for grouped data
      const scatterPlot = chartManager.createScatterPlot('test-canvas-4', plotData.groupedStats);
      expect(scatterPlot).toBeDefined();
      expect(scatterPlot.config.data.datasets).toHaveLength(3);
    });

    it('should handle control chart workflow', () => {
      // Generate control chart data
      const controlData = sampleData.generateControlChartData(50, {
        shiftPoint: 30,
        shiftMagnitude: 5
      });

      // Process data
      dataProcessor.setData(controlData.data);
      const plotData = dataProcessor.getPlotData();

      // Create individual values chart
      const individualChart = variabilityPlot.createIndividualPlot('test-canvas-5', plotData);
      expect(individualChart).toBeDefined();

      // Create moving range chart
      const movingRangeChart = variabilityPlot.createMovingRangePlot('test-canvas-6', plotData);
      expect(movingRangeChart).toBeDefined();
    });
  });

  describe('File Operations Integration', () => {
    it('should export and import data correctly', () => {
      // Generate and process data
      const rawData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      dataProcessor.setData(rawData);

      // Export as CSV
      const csvData = dataProcessor.exportData('csv');
      expect(csvData).toContain('Data');
      expect(csvData).toContain('Statistics');
      expect(csvData).toContain('1\n2\n3\n4\n5\n6\n7\n8\n9\n10');

      // Export as JSON
      const jsonData = dataProcessor.exportData('json');
      const parsed = JSON.parse(jsonData);
      expect(parsed.data).toEqual(rawData);
      expect(parsed.statistics).toBeDefined();

      // Test CSV parsing
      const csvContent = 'Value,Group\n1,A\n2,A\n3,B\n4,B';
      const parsedCSV = fileManager.parseCSV(csvContent);
      expect(parsedCSV.headers).toEqual(['Value', 'Group']);
      expect(parsedCSV.rows).toHaveLength(4);
    });

    it('should handle file validation', () => {
      // Mock file object
      const mockFile = {
        name: 'test.csv',
        type: 'text/csv',
        size: 1024
      };

      expect(fileManager.validateFileType(mockFile, ['text/csv'])).toBe(true);
      expect(fileManager.validateFileType(mockFile, ['image/png'])).toBe(false);
      expect(fileManager.validateFileSize(mockFile, 2048)).toBe(true);
      expect(fileManager.validateFileSize(mockFile, 512)).toBe(false);
    });
  });

  describe('Notification System Integration', () => {
    it('should create and manage notifications', () => {
      // Test basic notification creation
      const successId = notifications.show('Success message', 'success');
      const errorId = notifications.show('Error message', 'error');

      expect(typeof successId).toBe('string');
      expect(typeof errorId).toBe('string');
      expect(successId).not.toBe(errorId);

      expect(notifications.getCount()).toBe(2);
      expect(notifications.getCount('success')).toBe(1);
      expect(notifications.getCount('error')).toBe(1);
    });

    it('should handle notification lifecycle', () => {
      const notificationId = notifications.show('Test message', 'info');
      expect(notifications.exists(notificationId)).toBe(true);

      // Test removing specific notification
      notifications.remove(notificationId);
      expect(notifications.getCount()).toBeLessThanOrEqual(1); // Account for any remaining notifications
    });
  });

  describe('Sample Data Generation', () => {
    it('should generate various data types correctly', () => {
      // Normal distribution
      const normalData = sampleData.generateNormalDistribution(100, 50, 10);
      expect(normalData).toHaveLength(100);
      const mean = normalData.reduce((a, b) => a + b) / normalData.length;
      expect(mean).toBeGreaterThan(40); // Should be reasonably close to target mean
      expect(mean).toBeLessThan(60);

      // Skewed data
      const skewedData = sampleData.generateSkewedData(50);
      expect(skewedData).toHaveLength(50);
      expect(skewedData.every(val => val >= 0)).toBe(true); // Exponential distribution is positive

      // Uniform data
      const uniformData = sampleData.generateUniformData(30, 0, 100);
      expect(uniformData).toHaveLength(30);
      expect(uniformData.every(val => val >= 0 && val <= 100)).toBe(true);

      // Bimodal data
      const bimodalData = sampleData.generateBimodalData(60);
      expect(bimodalData).toHaveLength(60);

      // Data with outliers
      const outlierData = sampleData.generateDataWithOutliers(40, 20);
      expect(outlierData).toHaveLength(40);
    });

    it('should generate time series data', () => {
      const timeSeriesData = sampleData.generateTimeSeriesData(365, {
        startValue: 20,
        trend: 0.01,
        seasonality: 15
      });

      expect(timeSeriesData.data).toHaveLength(365);
      expect(timeSeriesData.timestamps).toHaveLength(365);
      expect(timeSeriesData.trend).toBeDefined();
      expect(timeSeriesData.trend.slope).toBe(0.01);
    });

    it('should provide sample datasets', () => {
      const availableDatasets = sampleData.getAvailableDatasets();
      expect(availableDatasets).toBeInstanceOf(Array);
      expect(availableDatasets.length).toBeGreaterThan(0);

      // Test getting a specific dataset
      const qualityData = sampleData.getSampleDataset('quality_measurements');
      expect(qualityData.data).toBeInstanceOf(Array);
      expect(qualityData.description).toBeDefined();
      expect(qualityData.units).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid data gracefully', () => {
      expect(() => {
        dataProcessor.setData([]);
      }).toThrow('No valid data after cleaning');

      expect(() => {
        dataProcessor.parseStringData('');
      }).toThrow('Input cannot be empty');

      expect(() => {
        dataProcessor.parseStringData('abc def ghi');
      }).toThrow('Invalid number');
    });

    it('should handle chart creation errors', () => {
      // Mock getElementById to return null for non-existent canvas
      const originalGetElementById = global.document.getElementById;
      global.document.getElementById = vi.fn((id) => {
        if (id === 'non-existent-canvas') return null;
        return originalGetElementById(id);
      });

      expect(() => {
        chartManager.createHistogram('non-existent-canvas', { bins: [], labels: [] });
      }).toThrow("Canvas element with ID 'non-existent-canvas' not found");

      expect(() => {
        chartManager.exportChart('non-existent-chart');
      }).toThrow("Chart with ID 'non-existent-chart' not found");

      // Restore original function
      global.document.getElementById = originalGetElementById;
    });

    it('should handle file operation errors', () => {
      // Test CSV parsing with empty content
      expect(() => {
        fileManager.parseCSV('');
      }).toThrow();

      expect(() => {
        fileManager.parseCSV('   ');
      }).toThrow();

      // Test JSON creation with valid data
      const jsonResult = fileManager.createJSON({ test: 'data' });
      expect(jsonResult).toContain('test');
      expect(jsonResult).toContain('data');
    });
  });

  describe('Performance and Memory', () => {
    it('should handle large datasets efficiently', () => {
      const largeDataset = sampleData.generateNormalDistribution(10000, 100, 15);
      expect(largeDataset).toHaveLength(10000);

      const startTime = performance.now();
      dataProcessor.setData(largeDataset);
      const plotData = dataProcessor.getPlotData();
      const endTime = performance.now();

      expect(plotData.statistics).toBeDefined();
      expect(endTime - startTime).toBeLessThan(1000); // Should process in less than 1 second
    });

    it('should clean up charts properly', () => {
      // Create multiple charts
      const data = sampleData.generateNormalDistribution(50);
      dataProcessor.setData(data);
      const plotData = dataProcessor.getPlotData();

      chartManager.createHistogram('chart1', plotData.histogram);
      chartManager.createBoxPlot('chart2', plotData.boxplot);
      chartManager.createVariabilityPlot('chart3', plotData);

      expect(chartManager.charts.size).toBe(3);

      // Clean up all charts
      chartManager.destroyAllCharts();
      expect(chartManager.charts.size).toBe(0);
    });
  });
});
