# Test Suite Documentation

This directory contains comprehensive tests for the Minitab-like Variability Plot Web UI application.

## Test Structure

### Unit Tests
- **`statistics.test.js`** - Tests for statistical calculation functions
- **`dataProcessor.test.js`** - Tests for data processing and validation
- **`chartManager.test.js`** - Tests for chart creation and management

### Integration Tests
- **`integration.test.js`** - End-to-end workflow tests covering complete data analysis pipelines

### Test Setup
- **`setup.js`** - Global test configuration and mocks

## Running Tests

### All Tests
```bash
npm test
```

### Watch Mode (for development)
```bash
npm test -- --watch
```

### Coverage Report
```bash
npm run test:coverage
```

### UI Mode (interactive)
```bash
npm run test:ui
```

## Test Categories

### 1. Statistical Functions (`statistics.test.js`)
Tests all statistical calculation functions including:
- Descriptive statistics (mean, median, mode, std dev, variance)
- Quartile calculations
- Skewness and kurtosis
- Confidence intervals
- Data validation and edge cases

**Coverage**: 23 tests covering all statistical functions

### 2. Data Processing (`dataProcessor.test.js`)
Tests data input, validation, and processing:
- String data parsing (space/comma separated)
- CSV data parsing and validation
- Data cleaning and filtering
- Grouped data handling
- Export functionality (CSV, JSON, TXT)
- Histogram and boxplot data generation

**Coverage**: 25 tests covering all data processing workflows

### 3. Chart Management (`chartManager.test.js`)
Tests chart creation and management:
- Histogram creation and configuration
- Box plot rendering with custom drawing
- Variability plot generation
- Scatter plot for grouped data
- Chart export and destruction
- Error handling for invalid inputs

**Coverage**: 17 tests covering all chart types and operations

### 4. Integration Tests (`integration.test.js`)
Tests complete workflows and system integration:
- End-to-end data analysis workflows
- File operations and data import/export
- Notification system functionality
- Sample data generation
- Error handling across components
- Performance with large datasets

**Coverage**: 15 test suites covering complete user workflows

## Test Data

### Sample Data Generation
The test suite includes utilities for generating various types of test data:
- Normal distributions
- Skewed data
- Grouped datasets
- Data with outliers
- Time series data
- Control chart data

### Mock Objects
Comprehensive mocking for:
- Chart.js library
- DOM elements and APIs
- File operations
- Canvas rendering context

## Test Utilities

### Custom Matchers
The tests use Vitest's built-in matchers plus custom validation:
- `toBeCloseTo()` for floating-point comparisons
- `toThrow()` for error testing
- `toHaveLength()` for array validation

### Mock Setup
- **Chart.js Mocking**: Complete mock implementation for all chart types
- **DOM Mocking**: Mock DOM elements for UI testing
- **File API Mocking**: Mock FileReader and file operations

## Coverage Goals

Current test coverage:
- **80/80 tests passing (100%)**
- **Functions**: >95% coverage
- **Lines**: >90% coverage
- **Branches**: >85% coverage

## Writing New Tests

### Test File Structure
```javascript
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { YourModule } from '../src/path/to/module.js';

describe('YourModule', () => {
  let instance;

  beforeEach(() => {
    instance = new YourModule();
  });

  afterEach(() => {
    // Cleanup
  });

  describe('method name', () => {
    it('should do something specific', () => {
      // Test implementation
      expect(result).toBe(expected);
    });
  });
});
```

### Best Practices
1. **Descriptive test names**: Use clear, specific descriptions
2. **Arrange-Act-Assert**: Structure tests clearly
3. **Test edge cases**: Include boundary conditions and error cases
4. **Mock external dependencies**: Isolate units under test
5. **Clean up**: Ensure tests don't affect each other

### Testing Statistical Functions
```javascript
it('should calculate mean correctly', () => {
  const data = [1, 2, 3, 4, 5];
  const result = calculateMean(data);
  expect(result).toBe(3);
});

it('should handle edge cases', () => {
  expect(() => calculateMean([])).toThrow();
  expect(calculateMean([5])).toBe(5);
});
```

### Testing Chart Components
```javascript
it('should create histogram chart', () => {
  const histogramData = { bins: [1, 2, 3], labels: ['A', 'B', 'C'] };
  const chart = chartManager.createHistogram('test-canvas', histogramData);
  
  expect(chart).toBeDefined();
  expect(chart.config.type).toBe('bar');
  expect(chart.config.data.datasets[0].data).toEqual([1, 2, 3]);
});
```

### Testing Async Operations
```javascript
it('should handle file loading', async () => {
  const mockFile = new File(['1,2,3'], 'test.csv', { type: 'text/csv' });
  const result = await fileManager.readFileAsText(mockFile);
  expect(result).toBe('1,2,3');
});
```

## Continuous Integration

The test suite is designed to run in CI/CD environments:
- **Fast execution**: Tests complete in under 5 seconds
- **Deterministic**: No flaky tests or random failures
- **Comprehensive**: Covers all critical functionality
- **Isolated**: Tests don't depend on external services

## Debugging Tests

### Running Specific Tests
```bash
# Run specific test file
npm test statistics.test.js

# Run specific test suite
npm test -- --grep "Statistical Functions"

# Run with verbose output
npm test -- --reporter=verbose
```

### Debug Mode
```bash
# Run tests with debugging
npm test -- --inspect-brk
```

### Common Issues
1. **Mock not working**: Check mock setup in `setup.js`
2. **Async test failing**: Ensure proper await/Promise handling
3. **Floating-point precision**: Use `toBeCloseTo()` for number comparisons
4. **DOM not available**: Verify jsdom environment is configured

## Performance Testing

The test suite includes performance benchmarks:
- Large dataset processing (10,000+ points)
- Chart rendering performance
- Memory usage validation
- Cleanup verification

## Future Enhancements

Planned test improvements:
1. **Visual regression testing** for chart outputs
2. **Accessibility testing** for UI components
3. **Cross-browser testing** automation
4. **Load testing** for large datasets
5. **Security testing** for file uploads

## Contributing

When adding new features:
1. Write tests first (TDD approach)
2. Ensure >90% code coverage
3. Include both positive and negative test cases
4. Update this documentation
5. Run full test suite before submitting
