/**
 * Data processing and validation utilities
 */

import { calculateDescriptiveStats } from './statistics.js';

/**
 * Parse and validate input data from various sources
 */
export class DataProcessor {
  constructor() {
    this.data = [];
    this.groups = [];
    this.metadata = {};
  }

  /**
   * Parse data from string input (space or comma separated)
   * @param {string} input - Raw data string
   * @returns {number[]} Parsed numerical array
   */
  parseStringData(input) {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }

    const cleanInput = input.trim();
    if (!cleanInput) {
      throw new Error('Input cannot be empty');
    }

    // Split by comma, semicolon, space, or tab
    const values = cleanInput
      .split(/[,;\s\t]+/)
      .filter(val => val.trim() !== '')
      .map(val => {
        const num = parseFloat(val.trim());
        if (isNaN(num)) {
          throw new Error(`Invalid number: "${val}"`);
        }
        return num;
      });

    if (values.length === 0) {
      throw new Error('No valid numbers found in input');
    }

    return values;
  }

  /**
   * Parse CSV data
   * @param {string} csvText - CSV content
   * @returns {Object} Parsed data with headers and rows
   */
  parseCSVData(csvText) {
    if (!csvText || csvText.trim() === '') {
      throw new Error('CSV data is empty');
    }

    const lines = csvText.trim().split('\n');
    if (lines.length === 0) {
      throw new Error('CSV data is empty');
    }

    const headers = lines[0].split(',').map(h => h.trim());
    const rows = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length !== headers.length) {
        throw new Error(`Row ${i} has ${values.length} values but expected ${headers.length}`);
      }
      rows.push(values);
    }

    return { headers, rows };
  }

  /**
   * Validate and clean dataset
   * @param {any[]} data - Raw data array
   * @returns {number[]} Cleaned numerical array
   */
  validateAndCleanData(data) {
    if (!Array.isArray(data)) {
      throw new Error('Data must be an array');
    }

    const cleaned = [];

    for (const val of data) {
      // Skip null, undefined, empty string
      if (val === null || val === undefined || val === '') {
        continue;
      }

      if (typeof val === 'string') {
        const num = parseFloat(val);
        if (!isNaN(num)) {
          cleaned.push(num);
        }
        // Skip invalid strings silently for metadata tracking
      } else if (typeof val === 'number' && !isNaN(val)) {
        cleaned.push(val);
      }
      // Skip other types silently for metadata tracking
    }

    if (cleaned.length === 0) {
      throw new Error('No valid data after cleaning');
    }

    return cleaned;
  }

  /**
   * Set data and calculate statistics
   * @param {any[]} rawData - Raw input data
   * @param {string[]} groups - Optional grouping data
   */
  setData(rawData, groups = []) {
    this.data = this.validateAndCleanData(rawData);
    this.groups = groups;
    this.metadata = {
      originalLength: rawData.length,
      cleanedLength: this.data.length,
      removedCount: rawData.length - this.data.length,
      hasGroups: groups.length > 0
    };
  }

  /**
   * Get basic statistics for the dataset
   * @returns {Object} Statistical summary
   */
  getStatistics() {
    if (this.data.length === 0) {
      throw new Error('No data available for analysis');
    }

    return calculateDescriptiveStats(this.data);
  }

  /**
   * Get grouped statistics
   * @returns {Object} Statistics by group
   */
  getGroupedStatistics() {
    if (this.groups.length === 0) {
      return { 'All Data': {
        ...this.getStatistics(),
        raw: this.data
      }};
    }

    const groupedData = {};
    const uniqueGroups = [...new Set(this.groups)];

    uniqueGroups.forEach(group => {
      const groupData = this.data.filter((_, index) => this.groups[index] === group);
      if (groupData.length > 0) {
        groupedData[group] = {
          ...calculateDescriptiveStats(groupData),
          raw: groupData  // Include raw data for plotting
        };
      }
    });

    return groupedData;
  }

  /**
   * Get data for plotting
   * @returns {Object} Data formatted for charts
   */
  getPlotData() {
    const stats = this.getStatistics();
    
    return {
      raw: this.data,
      groups: this.groups,
      statistics: stats,
      groupedStats: this.getGroupedStatistics(),
      metadata: this.metadata,
      histogram: this.generateHistogramData(),
      boxplot: this.generateBoxplotData()
    };
  }

  /**
   * Generate histogram data
   * @param {number} bins - Number of bins (default: auto-calculate)
   * @returns {Object} Histogram data
   */
  generateHistogramData(bins = null) {
    if (this.data.length === 0) return null;

    const min = Math.min(...this.data);
    const max = Math.max(...this.data);
    
    // Auto-calculate bins using Sturges' rule
    if (!bins) {
      bins = Math.ceil(Math.log2(this.data.length) + 1);
      bins = Math.max(5, Math.min(bins, 20)); // Limit between 5 and 20
    }

    const binWidth = (max - min) / bins;
    const binData = Array(bins).fill(0);
    const binLabels = [];

    // Create bin labels
    for (let i = 0; i < bins; i++) {
      const start = min + i * binWidth;
      const end = min + (i + 1) * binWidth;
      binLabels.push(`${start.toFixed(2)}-${end.toFixed(2)}`);
    }

    // Count data points in each bin
    this.data.forEach(value => {
      let binIndex = Math.floor((value - min) / binWidth);
      if (binIndex >= bins) binIndex = bins - 1; // Handle edge case
      binData[binIndex]++;
    });

    return {
      bins: binData,
      labels: binLabels,
      binWidth,
      min,
      max
    };
  }

  /**
   * Generate box plot data
   * @returns {Object} Box plot data
   */
  generateBoxplotData() {
    if (this.data.length === 0) return null;

    const sorted = [...this.data].sort((a, b) => a - b);
    const stats = this.getStatistics();
    
    // Calculate outliers (values beyond 1.5 * IQR from quartiles)
    const lowerFence = stats.q1 - 1.5 * stats.iqr;
    const upperFence = stats.q3 + 1.5 * stats.iqr;
    
    const outliers = this.data.filter(val => val < lowerFence || val > upperFence);
    const nonOutliers = this.data.filter(val => val >= lowerFence && val <= upperFence);
    
    return {
      min: stats.min,
      q1: stats.q1,
      median: stats.median,
      q3: stats.q3,
      max: stats.max,
      outliers,
      whiskerLow: nonOutliers.length > 0 ? Math.min(...nonOutliers) : stats.min,
      whiskerHigh: nonOutliers.length > 0 ? Math.max(...nonOutliers) : stats.max,
      iqr: stats.iqr
    };
  }

  /**
   * Export data in various formats
   * @param {string} format - Export format ('csv', 'json', 'txt')
   * @returns {string} Formatted data
   */
  exportData(format = 'csv') {
    switch (format.toLowerCase()) {
      case 'csv':
        return this.exportCSV();
      case 'json':
        return this.exportJSON();
      case 'txt':
        return this.exportTXT();
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  exportCSV() {
    const stats = this.getStatistics();
    let csv = 'Data\n';
    csv += this.data.join('\n');
    csv += '\n\nStatistics\n';
    csv += Object.entries(stats)
      .map(([key, value]) => `${key},${value}`)
      .join('\n');
    return csv;
  }

  exportJSON() {
    return JSON.stringify({
      data: this.data,
      groups: this.groups,
      statistics: this.getStatistics(),
      metadata: this.metadata
    }, null, 2);
  }

  exportTXT() {
    const stats = this.getStatistics();
    let txt = 'Dataset Summary\n';
    txt += '===============\n\n';
    txt += `Data: ${this.data.join(', ')}\n\n`;
    txt += 'Statistics:\n';
    Object.entries(stats).forEach(([key, value]) => {
      txt += `${key}: ${typeof value === 'number' ? value.toFixed(4) : value}\n`;
    });
    return txt;
  }
}
