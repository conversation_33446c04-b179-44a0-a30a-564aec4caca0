import { describe, it, expect } from 'vitest';
import {
  calculateDescriptiveStats,
  calculateMean,
  calculateMedian,
  calculateMode,
  calculateStandardDeviation,
  calculateVariance,
  calculateQuartile,
  calculateSkewness,
  calculateKurtosis,
  calculateConfidenceInterval
} from '../src/data/statistics.js';

describe('Statistics Module', () => {
  const testData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  const smallData = [1, 2, 3];
  const duplicateData = [1, 1, 2, 2, 2, 3];

  describe('calculateMean', () => {
    it('should calculate mean correctly', () => {
      expect(calculateMean(testData)).toBe(5.5);
      expect(calculateMean(smallData)).toBe(2);
    });

    it('should handle single value', () => {
      expect(calculateMean([5])).toBe(5);
    });
  });

  describe('calculateMedian', () => {
    it('should calculate median for even length array', () => {
      expect(calculateMedian([1, 2, 3, 4])).toBe(2.5);
    });

    it('should calculate median for odd length array', () => {
      expect(calculateMedian([1, 2, 3, 4, 5])).toBe(3);
    });

    it('should handle single value', () => {
      expect(calculateMedian([5])).toBe(5);
    });
  });

  describe('calculateMode', () => {
    it('should find single mode', () => {
      expect(calculateMode(duplicateData)).toEqual([2]);
    });

    it('should find multiple modes', () => {
      const result = calculateMode([1, 1, 2, 2]);
      expect(result.sort()).toEqual([1, 2]);
    });

    it('should handle no mode (all unique)', () => {
      const result = calculateMode([1, 2, 3]);
      expect(result.sort()).toEqual([1, 2, 3]);
    });
  });

  describe('calculateStandardDeviation', () => {
    it('should calculate sample standard deviation', () => {
      const result = calculateStandardDeviation(testData);
      expect(result).toBeCloseTo(3.0277, 3);
    });

    it('should calculate population standard deviation', () => {
      const result = calculateStandardDeviation(testData, false);
      expect(result).toBeCloseTo(2.8723, 3);
    });
  });

  describe('calculateVariance', () => {
    it('should calculate sample variance', () => {
      const result = calculateVariance(testData);
      expect(result).toBeCloseTo(9.1667, 3);
    });

    it('should calculate population variance', () => {
      const result = calculateVariance(testData, false);
      expect(result).toBeCloseTo(8.25, 3);
    });
  });

  describe('calculateQuartile', () => {
    it('should calculate Q1', () => {
      const result = calculateQuartile(testData, 0.25);
      expect(result).toBeCloseTo(3.25, 2);
    });

    it('should calculate Q3', () => {
      const result = calculateQuartile(testData, 0.75);
      expect(result).toBeCloseTo(7.75, 2);
    });

    it('should handle edge cases', () => {
      expect(calculateQuartile([1], 0.5)).toBe(1);
      expect(calculateQuartile([1, 2], 0.5)).toBe(1.5);
    });
  });

  describe('calculateDescriptiveStats', () => {
    it('should calculate all statistics correctly', () => {
      const stats = calculateDescriptiveStats(testData);
      
      expect(stats.count).toBe(10);
      expect(stats.mean).toBe(5.5);
      expect(stats.median).toBe(5.5);
      expect(stats.min).toBe(1);
      expect(stats.max).toBe(10);
      expect(stats.range).toBe(9);
      expect(stats.standardDeviation).toBeCloseTo(3.0277, 3);
      expect(stats.variance).toBeCloseTo(9.1667, 3);
      expect(stats.q1).toBeCloseTo(3.25, 2);
      expect(stats.q3).toBeCloseTo(7.75, 2);
      expect(stats.iqr).toBeCloseTo(4.5, 2);
    });

    it('should handle invalid input', () => {
      expect(() => calculateDescriptiveStats([])).toThrow();
      expect(() => calculateDescriptiveStats(null)).toThrow();
      expect(() => calculateDescriptiveStats('not an array')).toThrow();
    });

    it('should filter out invalid values', () => {
      const mixedData = [1, 2, NaN, 3, null, 4, undefined, 5];
      const stats = calculateDescriptiveStats(mixedData);
      expect(stats.count).toBe(5);
      expect(stats.mean).toBe(3);
    });
  });

  describe('calculateSkewness', () => {
    it('should calculate skewness', () => {
      const result = calculateSkewness(testData);
      expect(result).toBeCloseTo(0, 1); // Symmetric data should have ~0 skewness
    });

    it('should handle right-skewed data', () => {
      const rightSkewed = [1, 1, 1, 2, 2, 3, 4, 5, 6, 10];
      const result = calculateSkewness(rightSkewed);
      expect(result).toBeGreaterThan(0);
    });
  });

  describe('calculateKurtosis', () => {
    it('should calculate kurtosis', () => {
      const result = calculateKurtosis(testData);
      expect(typeof result).toBe('number');
      expect(isFinite(result)).toBe(true);
    });
  });

  describe('calculateConfidenceInterval', () => {
    it('should calculate 95% confidence interval', () => {
      const ci = calculateConfidenceInterval(testData);
      expect(ci.lower).toBeLessThan(ci.upper);
      expect(ci.margin).toBeGreaterThan(0);
      expect(typeof ci.lower).toBe('number');
      expect(typeof ci.upper).toBe('number');
    });

    it('should calculate different confidence levels', () => {
      const ci80 = calculateConfidenceInterval(testData, 0.80);
      const ci95 = calculateConfidenceInterval(testData, 0.95);

      expect(ci80.margin).toBeLessThan(ci95.margin);
    });
  });
});
