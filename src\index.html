<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Minitab-like Variability Plot Web UI</title>
  <link rel="stylesheet" href="./styles/main.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div id="app">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <h1 class="logo">
            <span class="logo-icon">📊</span>
            Minitab-like Variability Plot
          </h1>
          <nav class="nav">
            <button class="nav-btn" data-tab="data">Data Input</button>
            <button class="nav-btn active" data-tab="analysis">Analysis</button>
            <button class="nav-btn" data-tab="export">Export</button>
          </nav>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main">
      <div class="container">
        <!-- Data Input Tab -->
        <section id="data-tab" class="tab-content">
          <div class="card">
            <h2 class="card-title">Data Input</h2>
            <div class="input-methods">
              <div class="input-method">
                <h3>Manual Entry</h3>
                <div class="form-group">
                  <label for="manual-data">Enter data (space or comma separated):</label>
                  <textarea 
                    id="manual-data" 
                    class="form-control" 
                    placeholder="1 2 3 4 5 or 1,2,3,4,5"
                    rows="4"
                  ></textarea>
                </div>
                <div class="form-group">
                  <label for="group-data">Group labels (optional):</label>
                  <textarea 
                    id="group-data" 
                    class="form-control" 
                    placeholder="A A B B C or A,A,B,B,C"
                    rows="2"
                  ></textarea>
                </div>
                <button id="load-manual-data" class="btn btn-primary">Load Data</button>
              </div>

              <div class="input-method">
                <h3>CSV File Upload</h3>
                <div class="file-upload">
                  <input type="file" id="csv-file" accept=".csv" class="file-input">
                  <label for="csv-file" class="file-label">
                    <span class="file-icon">📁</span>
                    Choose CSV File
                  </label>
                </div>
                <div id="csv-preview" class="csv-preview hidden"></div>
              </div>

              <div class="input-method">
                <h3>Sample Data</h3>
                <div class="sample-data-buttons">
                  <button class="btn btn-secondary" data-sample="normal">Normal Distribution</button>
                  <button class="btn btn-secondary" data-sample="skewed">Skewed Data</button>
                  <button class="btn btn-secondary" data-sample="grouped">Grouped Data</button>
                  <button class="btn btn-secondary" data-sample="outliers">Data with Outliers</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Summary -->
          <div id="data-summary" class="card hidden">
            <h2 class="card-title">Data Summary</h2>
            <div class="summary-grid">
              <div class="summary-item">
                <span class="summary-label">Count:</span>
                <span id="summary-count" class="summary-value">-</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Mean:</span>
                <span id="summary-mean" class="summary-value">-</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Median:</span>
                <span id="summary-median" class="summary-value">-</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Std Dev:</span>
                <span id="summary-std" class="summary-value">-</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Min:</span>
                <span id="summary-min" class="summary-value">-</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">Max:</span>
                <span id="summary-max" class="summary-value">-</span>
              </div>
            </div>
          </div>
        </section>

        <!-- Analysis Tab -->
        <section id="analysis-tab" class="tab-content active">
          <div class="analysis-layout">
            <!-- Controls Panel -->
            <div class="controls-panel">
              <div class="card">
                <h3 class="card-title">Plot Options</h3>
                <div class="form-group">
                  <label for="plot-type">Plot Type:</label>
                  <select id="plot-type" class="form-control">
                    <option value="histogram">Histogram</option>
                    <option value="boxplot">Box Plot</option>
                    <option value="variability">Variability Plot</option>
                    <option value="individual">Individual Values (I-Chart)</option>
                    <option value="movingRange">Moving Range (MR-Chart)</option>
                    <option value="scatter">Scatter Plot</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="confidence-level">Confidence Level:</label>
                  <select id="confidence-level" class="form-control">
                    <option value="0.80">80%</option>
                    <option value="0.90">90%</option>
                    <option value="0.95" selected>95%</option>
                    <option value="0.99">99%</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="chart-title">Chart Title:</label>
                  <input type="text" id="chart-title" class="form-control" placeholder="Enter chart title">
                </div>

                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="show-mean" checked>
                    <span class="checkmark"></span>
                    Show Mean Line
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="show-confidence">
                    <span class="checkmark"></span>
                    Show Confidence Interval
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="show-grid" checked>
                    <span class="checkmark"></span>
                    Show Grid
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="show-legend" checked>
                    <span class="checkmark"></span>
                    Show Legend
                  </label>
                </div>

                <button id="generate-plot" class="btn btn-primary btn-full">Generate Plot</button>
                <button id="test-chart" class="btn btn-secondary btn-full" style="margin-top: 10px;">Test Chart Creation</button>
              </div>

              <!-- Statistics Panel -->
              <div class="card">
                <h3 class="card-title">Statistics</h3>
                <div id="statistics-content" class="statistics-content">
                  <p class="no-data">Load data to see statistics</p>
                </div>
              </div>
            </div>

            <!-- Chart Area -->
            <div class="chart-area">
              <div class="card chart-card">
                <div class="chart-header">
                  <h3 id="chart-title-display" class="chart-title">Chart</h3>
                  <div class="chart-actions">
                    <button id="export-chart" class="btn btn-icon" title="Export Chart">
                      <span>💾</span>
                    </button>
                    <button id="fullscreen-chart" class="btn btn-icon" title="Fullscreen">
                      <span>⛶</span>
                    </button>
                  </div>
                </div>
                <div class="chart-container">
                  <canvas id="main-chart"></canvas>
                  <div id="chart-placeholder" class="chart-placeholder">
                    <div class="placeholder-content">
                      <span class="placeholder-icon">📊</span>
                      <h4>No Chart Generated</h4>
                      <p>Load data and select a plot type to generate a chart</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Export Tab -->
        <section id="export-tab" class="tab-content">
          <div class="card">
            <h2 class="card-title">Export Options</h2>
            <div class="export-options">
              <div class="export-section">
                <h3>Export Data</h3>
                <div class="export-buttons">
                  <button id="export-csv" class="btn btn-secondary">Export as CSV</button>
                  <button id="export-json" class="btn btn-secondary">Export as JSON</button>
                  <button id="export-txt" class="btn btn-secondary">Export as Text</button>
                </div>
              </div>

              <div class="export-section">
                <h3>Export Chart</h3>
                <div class="export-buttons">
                  <button id="export-png" class="btn btn-secondary">Export as PNG</button>
                  <button id="export-jpeg" class="btn btn-secondary">Export as JPEG</button>
                </div>
              </div>

              <div class="export-section">
                <h3>Export Report</h3>
                <div class="form-group">
                  <label for="report-title">Report Title:</label>
                  <input type="text" id="report-title" class="form-control" placeholder="Statistical Analysis Report">
                </div>
                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="include-data" checked>
                    <span class="checkmark"></span>
                    Include Raw Data
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="include-statistics" checked>
                    <span class="checkmark"></span>
                    Include Statistics
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="include-chart" checked>
                    <span class="checkmark"></span>
                    Include Chart
                  </label>
                </div>
                <button id="export-report" class="btn btn-primary">Generate Report</button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <p>&copy; 2025 Minitab-like Variability Plot Web UI. Built with modern web technologies.</p>
      </div>
    </footer>
  </div>

  <!-- Loading Overlay -->
  <div id="loading-overlay" class="loading-overlay hidden">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>Processing...</p>
    </div>
  </div>

  <!-- Notification System -->
  <div id="notifications" class="notifications"></div>

  <script type="module" src="./index.js"></script>
</body>
</html>
