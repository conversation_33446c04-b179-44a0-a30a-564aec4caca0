# Deployment Guide - Minitab-like Variability Plot Web UI

## Overview

This guide covers deployment options for the Minitab-like Variability Plot Web UI application, from local development to production environments.

## Prerequisites

### System Requirements

**Development Environment**:
- Node.js 16+ (recommended: 18+)
- npm 8+ or yarn 1.22+
- Modern web browser
- Git (for version control)

**Production Environment**:
- Web server (Apache, Nginx, or CDN)
- HTTPS support (recommended)
- Modern browser support

### Browser Compatibility

**Supported Browsers**:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

**Required Features**:
- ES2020 support
- Canvas API
- File API
- Fetch API

## Local Development

### Setup

1. **Clone Repository**:
```bash
git clone <repository-url>
cd minitab-variability-plot-ui
```

2. **Install Dependencies**:
```bash
npm install
```

3. **Start Development Server**:
```bash
npm run dev
```

4. **Access Application**:
   - URL: `http://localhost:3000`
   - Hot reload enabled
   - Development tools available

### Development Scripts

```bash
# Start development server
npm run dev

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in UI mode
npm run test:ui

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

## Production Build

### Build Process

1. **Create Production Build**:
```bash
npm run build
```

2. **Preview Build Locally**:
```bash
npm run preview
```

3. **Build Output**:
   - Location: `dist/` directory
   - Optimized and minified files
   - Static assets included

### Build Configuration

**Vite Configuration** (`vite.config.js`):
```javascript
export default defineConfig({
  root: 'src',
  build: {
    outDir: '../dist',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['chart.js'],
          utils: ['papaparse']
        }
      }
    }
  }
});
```

### Build Optimization

**Automatic Optimizations**:
- Code minification
- Tree shaking
- Asset optimization
- Chunk splitting
- Gzip compression

**Manual Optimizations**:
- Image compression
- Font subsetting
- Critical CSS inlining

## Deployment Options

### 1. Static Hosting (Recommended)

#### Netlify

1. **Connect Repository**:
   - Link GitHub/GitLab repository
   - Auto-deploy on commits

2. **Build Settings**:
   - Build command: `npm run build`
   - Publish directory: `dist`

3. **Environment Variables**:
   - Set in Netlify dashboard
   - Available during build

**netlify.toml**:
```toml
[build]
  command = "npm run build"
  publish = "dist"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

#### Vercel

1. **Deploy Command**:
```bash
npx vercel
```

2. **Configuration** (`vercel.json`):
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite"
}
```

#### GitHub Pages

1. **GitHub Actions** (`.github/workflows/deploy.yml`):
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
```

### 2. Traditional Web Servers

#### Apache

**Configuration** (`.htaccess`):
```apache
RewriteEngine On
RewriteRule ^(?!.*\.).*$ /index.html [L]

# Security headers
Header always set X-Frame-Options DENY
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"

# Caching
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
  ExpiresActive On
  ExpiresDefault "access plus 1 year"
</FilesMatch>
```

#### Nginx

**Configuration**:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/minitab-ui/dist;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_types text/css application/javascript application/json;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 3. Content Delivery Networks (CDN)

#### AWS CloudFront

1. **S3 Bucket Setup**:
   - Upload build files to S3
   - Configure static website hosting

2. **CloudFront Distribution**:
   - Origin: S3 bucket
   - Default root object: `index.html`
   - Error pages: redirect to `index.html`

3. **Deployment Script**:
```bash
#!/bin/bash
npm run build
aws s3 sync dist/ s3://your-bucket-name --delete
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

## Environment Configuration

### Environment Variables

**Development** (`.env.development`):
```
VITE_APP_TITLE=Minitab UI (Development)
VITE_API_URL=http://localhost:3000
VITE_DEBUG=true
```

**Production** (`.env.production`):
```
VITE_APP_TITLE=Minitab-like Variability Plot UI
VITE_API_URL=https://your-domain.com
VITE_DEBUG=false
```

### Configuration Management

**Access in Code**:
```javascript
const config = {
  title: import.meta.env.VITE_APP_TITLE,
  apiUrl: import.meta.env.VITE_API_URL,
  debug: import.meta.env.VITE_DEBUG === 'true'
};
```

## Security Configuration

### Content Security Policy

**CSP Header**:
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self'
```

### HTTPS Configuration

**Force HTTPS** (Nginx):
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Your application configuration
}
```

## Performance Optimization

### Build Optimizations

1. **Bundle Analysis**:
```bash
npm run build -- --analyze
```

2. **Code Splitting**:
```javascript
// Dynamic imports for large dependencies
const Chart = await import('chart.js');
```

3. **Asset Optimization**:
   - Image compression
   - Font subsetting
   - CSS purging

### Runtime Optimizations

1. **Service Worker** (`public/sw.js`):
```javascript
const CACHE_NAME = 'minitab-ui-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/assets/index.css',
  '/assets/index.js'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});
```

2. **Resource Hints**:
```html
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="dns-prefetch" href="https://fonts.gstatic.com">
```

## Monitoring and Analytics

### Error Tracking

**Sentry Integration**:
```javascript
import * as Sentry from '@sentry/browser';

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
  environment: import.meta.env.MODE
});
```

### Performance Monitoring

**Web Vitals**:
```javascript
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

### Analytics

**Google Analytics 4**:
```javascript
// gtag.js
gtag('config', 'GA_MEASUREMENT_ID', {
  page_title: 'Minitab UI',
  page_location: window.location.href
});
```

## Continuous Integration/Deployment

### GitHub Actions

**Complete CI/CD Pipeline**:
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - run: npm ci
      - run: npm run lint
      - run: npm run test:coverage
      - uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - run: npm ci
      - run: npm run build
      - uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist/
      - name: Deploy to Production
        run: |
          # Your deployment script here
```

## Troubleshooting

### Common Issues

**Build Failures**:
- Check Node.js version compatibility
- Clear `node_modules` and reinstall
- Verify environment variables

**Deployment Issues**:
- Check file permissions
- Verify server configuration
- Test with local preview

**Performance Issues**:
- Analyze bundle size
- Check network requests
- Monitor memory usage

### Debug Tools

**Development**:
- Browser DevTools
- Vite debug mode
- Source maps enabled

**Production**:
- Error tracking (Sentry)
- Performance monitoring
- User feedback collection

## Maintenance

### Regular Tasks

1. **Dependency Updates**:
```bash
npm audit
npm update
```

2. **Security Scanning**:
```bash
npm audit --audit-level moderate
```

3. **Performance Testing**:
   - Lighthouse audits
   - Load testing
   - User experience monitoring

### Backup Strategy

1. **Source Code**: Version control (Git)
2. **Build Artifacts**: CI/CD pipeline storage
3. **Configuration**: Environment variable backup
4. **Documentation**: Keep updated with changes

## Support and Resources

### Documentation
- User Guide: `docs/USER_GUIDE.md`
- API Documentation: `docs/API_DOCUMENTATION.md`
- Test Documentation: `tests/README.md`

### Community
- Issue Tracker: GitHub Issues
- Discussions: GitHub Discussions
- Contributing: `CONTRIBUTING.md`

### Professional Support
- Enterprise support available
- Custom deployment assistance
- Training and consultation
