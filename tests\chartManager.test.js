import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ChartManager } from '../src/charts/chartManager.js';

// Mock Chart.js
vi.mock('chart.js', () => {
  const mockChart = vi.fn().mockImplementation((ctx, config) => ({
    ctx,
    config,
    data: config.data,
    options: config.options,
    destroy: vi.fn(),
    update: vi.fn(),
    resize: vi.fn(),
    toBase64Image: vi.fn(() => 'data:image/png;base64,mock-image-data'),
    chartArea: { left: 50, right: 350, top: 50, bottom: 250 },
    scales: {
      y: {
        getPixelForValue: vi.fn((value) => 200 - value * 10) // Mock pixel conversion
      }
    }
  }));

  mockChart.register = vi.fn();

  return {
    Chart: mockChart,
    CategoryScale: vi.fn(),
    LinearScale: vi.fn(),
    BarElement: vi.fn(),
    LineElement: vi.fn(),
    PointElement: vi.fn(),
    Title: vi.fn(),
    Tooltip: vi.fn(),
    Legend: vi.fn(),
    Filler: vi.fn()
  };
});

describe('ChartManager', () => {
  let chartManager;
  let mockCanvas;

  beforeEach(() => {
    chartManager = new ChartManager();
    
    // Mock canvas element
    mockCanvas = {
      id: 'test-canvas',
      getContext: vi.fn(() => ({
        save: vi.fn(),
        restore: vi.fn(),
        fillRect: vi.fn(),
        strokeRect: vi.fn(),
        beginPath: vi.fn(),
        moveTo: vi.fn(),
        lineTo: vi.fn(),
        stroke: vi.fn()
      }))
    };
    
    // Mock document.getElementById
    global.document = {
      getElementById: vi.fn((id) => id === 'test-canvas' ? mockCanvas : null)
    };
  });

  afterEach(() => {
    chartManager.destroyAllCharts();
  });

  describe('createHistogram', () => {
    it('should create a histogram chart', () => {
      const histogramData = {
        bins: [2, 5, 8, 3, 1],
        labels: ['0-2', '2-4', '4-6', '6-8', '8-10'],
        binWidth: 2,
        min: 0,
        max: 10
      };

      const chart = chartManager.createHistogram('test-canvas', histogramData);
      
      expect(chart).toBeDefined();
      expect(chart.config.type).toBe('bar');
      expect(chart.config.data.labels).toEqual(histogramData.labels);
      expect(chart.config.data.datasets[0].data).toEqual(histogramData.bins);
      expect(chartManager.getChart('test-canvas')).toBe(chart);
    });

    it('should throw error for invalid canvas ID', () => {
      const histogramData = { bins: [1, 2, 3], labels: ['A', 'B', 'C'] };
      
      expect(() => {
        chartManager.createHistogram('invalid-canvas', histogramData);
      }).toThrow("Canvas element with ID 'invalid-canvas' not found");
    });

    it('should apply custom options', () => {
      const histogramData = { bins: [1, 2, 3], labels: ['A', 'B', 'C'] };
      const options = {
        title: 'Custom Histogram',
        backgroundColor: '#ff0000',
        showLegend: false
      };

      const chart = chartManager.createHistogram('test-canvas', histogramData, options);
      
      expect(chart.config.options.plugins.title.text).toBe('Custom Histogram');
      expect(chart.config.data.datasets[0].backgroundColor).toBe('#ff0000');
      expect(chart.config.options.plugins.legend.display).toBe(false);
    });
  });

  describe('createBoxPlot', () => {
    it('should create a box plot chart', () => {
      const boxplotData = {
        min: 1,
        q1: 3,
        median: 5,
        q3: 7,
        max: 10,
        outliers: [0, 12],
        whiskerLow: 2,
        whiskerHigh: 9,
        iqr: 4
      };

      const chart = chartManager.createBoxPlot('test-canvas', boxplotData);
      
      expect(chart).toBeDefined();
      expect(chart.config.type).toBe('scatter');
      expect(chart.config.data.datasets[0].label).toBe('Outliers');
      expect(chart.config.data.datasets[0].data).toHaveLength(2);
    });

    it('should handle empty outliers', () => {
      const boxplotData = {
        min: 1, q1: 3, median: 5, q3: 7, max: 10,
        outliers: [], whiskerLow: 1, whiskerHigh: 10, iqr: 4
      };

      const chart = chartManager.createBoxPlot('test-canvas', boxplotData);
      
      expect(chart.config.data.datasets[0].data).toHaveLength(0);
    });
  });

  describe('createVariabilityPlot', () => {
    it('should create a variability plot', () => {
      const data = {
        raw: [1, 2, 3, 4, 5],
        statistics: { mean: 3 },
        confidenceInterval: { lower: 2, upper: 4 }
      };

      const options = {
        showMean: true,
        showConfidenceInterval: true
      };

      const chart = chartManager.createVariabilityPlot('test-canvas', data, options);
      
      expect(chart).toBeDefined();
      expect(chart.config.type).toBe('line');
      expect(chart.config.data.datasets).toHaveLength(4); // Data + Mean + CI upper + CI lower
      expect(chart.config.data.datasets[0].label).toContain('Data Points');
      expect(chart.config.data.datasets[1].label).toBe('Mean');
    });

    it('should create plot without confidence interval', () => {
      const data = {
        raw: [1, 2, 3, 4, 5],
        statistics: { mean: 3 }
      };

      const chart = chartManager.createVariabilityPlot('test-canvas', data);
      
      expect(chart.config.data.datasets).toHaveLength(2); // Data + Mean only
    });
  });

  describe('createScatterPlot', () => {
    it('should create a scatter plot for grouped data', () => {
      const groupedData = {
        'Group A': { raw: [1, 2, 3] },
        'Group B': { raw: [4, 5, 6] }
      };

      const chart = chartManager.createScatterPlot('test-canvas', groupedData);
      
      expect(chart).toBeDefined();
      expect(chart.config.type).toBe('scatter');
      expect(chart.config.data.datasets).toHaveLength(2);
      expect(chart.config.data.datasets[0].label).toBe('Group A');
      expect(chart.config.data.datasets[1].label).toBe('Group B');
    });
  });

  describe('chart management', () => {
    it('should destroy specific chart', () => {
      const histogramData = { bins: [1, 2, 3], labels: ['A', 'B', 'C'] };
      const chart = chartManager.createHistogram('test-canvas', histogramData);
      
      expect(chartManager.getChart('test-canvas')).toBe(chart);
      
      chartManager.destroyChart('test-canvas');
      
      expect(chartManager.getChart('test-canvas')).toBeNull();
      expect(chart.destroy).toHaveBeenCalled();
    });

    it('should destroy all charts', () => {
      const histogramData = { bins: [1, 2, 3], labels: ['A', 'B', 'C'] };
      const chart1 = chartManager.createHistogram('test-canvas', histogramData);
      
      // Mock second canvas
      global.document.getElementById = vi.fn((id) => {
        if (id === 'test-canvas' || id === 'test-canvas-2') {
          return mockCanvas;
        }
        return null;
      });
      
      const chart2 = chartManager.createHistogram('test-canvas-2', histogramData);
      
      expect(chartManager.charts.size).toBe(2);
      
      chartManager.destroyAllCharts();
      
      expect(chartManager.charts.size).toBe(0);
      expect(chart1.destroy).toHaveBeenCalled();
      expect(chart2.destroy).toHaveBeenCalled();
    });

    it('should export chart as image', () => {
      const histogramData = { bins: [1, 2, 3], labels: ['A', 'B', 'C'] };
      const chart = chartManager.createHistogram('test-canvas', histogramData);
      
      const imageData = chartManager.exportChart('test-canvas');
      
      expect(imageData).toBe('data:image/png;base64,mock-image-data');
      expect(chart.toBase64Image).toHaveBeenCalledWith('image/png', 1.0);
    });

    it('should throw error when exporting non-existent chart', () => {
      expect(() => {
        chartManager.exportChart('non-existent');
      }).toThrow("Chart with ID 'non-existent' not found");
    });
  });

  describe('drawBoxPlot', () => {
    it('should draw box plot elements', () => {
      const boxplotData = {
        min: 1, q1: 3, median: 5, q3: 7, max: 10,
        outliers: [], whiskerLow: 2, whiskerHigh: 9, iqr: 4
      };

      const mockChart = {
        ctx: mockCanvas.getContext('2d'),
        chartArea: { left: 50, right: 350, top: 50, bottom: 250 },
        scales: {
          y: {
            getPixelForValue: vi.fn((value) => 200 - value * 10)
          }
        }
      };

      chartManager.drawBoxPlot(mockChart, boxplotData);
      
      const ctx = mockChart.ctx;
      expect(ctx.save).toHaveBeenCalled();
      expect(ctx.restore).toHaveBeenCalled();
      expect(ctx.fillRect).toHaveBeenCalled();
      expect(ctx.strokeRect).toHaveBeenCalled();
    });
  });

  describe('getDefaultOptions', () => {
    it('should return histogram default options', () => {
      const options = chartManager.getDefaultOptions('histogram');
      
      expect(options.scales.y.beginAtZero).toBe(true);
      expect(options.scales.y.title.text).toBe('Frequency');
      expect(options.scales.x.title.text).toBe('Value Range');
    });

    it('should return boxplot default options', () => {
      const options = chartManager.getDefaultOptions('boxplot');
      
      expect(options.scales.y.title.text).toBe('Value');
    });

    it('should return variability default options', () => {
      const options = chartManager.getDefaultOptions('variability');
      
      expect(options.scales.x.title.text).toBe('Observation');
      expect(options.scales.y.title.text).toBe('Value');
      expect(options.interaction.mode).toBe('index');
    });

    it('should return base options for unknown type', () => {
      const options = chartManager.getDefaultOptions('unknown');
      
      expect(options.responsive).toBe(true);
      expect(options.maintainAspectRatio).toBe(false);
    });
  });
});
